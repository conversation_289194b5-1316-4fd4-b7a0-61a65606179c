package com.diecolor.project.student.controller;

import com.diecolor.framework.aspectj.lang.annotation.Anonymous;
import com.diecolor.framework.web.domain.AjaxResult;
import com.diecolor.project.student.domain.StLoginUser;
import com.diecolor.project.student.domain.dto.StudentCourseInfoDTO;
import com.diecolor.project.student.service.IStCourseService;
import com.diecolor.project.student.util.StAuthUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 学生端课程Controller
 * 
 * <AUTHOR>
 */
@Slf4j
@Anonymous
@RestController
@RequestMapping("/student/course")
@RequiredArgsConstructor
public class StCourseController {

    private final IStCourseService stCourseService;

    /**
     * 获取当前学生的进行中课程列表
     * 只返回状态为1（进行中）的课程
     *
     * @return 进行中的课程列表
     */
    @GetMapping("/list")
    public AjaxResult getActiveCourseList() {
        // 检查用户是否已登录，未登录会抛出401异常
        StLoginUser loginUser = StAuthUtil.getRequiredLoginUser();
        
        log.info("[StCourseController] 获取用户 {} 的进行中课程列表", loginUser.getUsername());
        
        List<StudentCourseInfoDTO> courseList = stCourseService.getActiveCourseList();
        
        log.info("[StCourseController] 用户 {} 的进行中课程数量: {}", loginUser.getUsername(), courseList.size());
        
        return AjaxResult.success("获取课程列表成功", courseList);
    }

    /**
     * 根据课程ID获取课程详细信息
     *
     * @param courseId 课程ID
     * @return 课程详细信息
     */
    @GetMapping("/info/{courseId}")
    public AjaxResult getCourseInfo(@PathVariable String courseId) {
        // 检查用户是否已登录
        StLoginUser loginUser = StAuthUtil.getRequiredLoginUser();
        
        log.info("[StCourseController] 获取用户 {} 的课程 {} 详细信息", loginUser.getUsername(), courseId);
        
        StudentCourseInfoDTO courseInfo = stCourseService.getCourseInfo(courseId);
        
        if (courseInfo == null) {
            return AjaxResult.error("课程信息不存在");
        }
        
        return AjaxResult.success("获取课程信息成功", courseInfo);
    }

    /**
     * 获取当前学生的所有课程列表
     * 用于课程首页的"我的课程"列表显示
     *
     * @param currentCourseId 当前课程ID（可选，如果提供则会在结果中过滤掉）
     * @return 所有课程列表
     */
    @GetMapping("/my-courses")
    public AjaxResult getMyCourseList(@RequestParam(required = false) String currentCourseId) {
        // 检查用户是否已登录
        StAuthUtil.getRequiredLoginUser();
        List<StudentCourseInfoDTO> courseList = stCourseService.getMyCourseList(currentCourseId);
        return AjaxResult.success("获取我的课程列表成功", courseList);
    }
}
