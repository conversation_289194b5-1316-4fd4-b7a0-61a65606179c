package com.diecolor.project.student.controller;

import com.diecolor.framework.aspectj.lang.annotation.Anonymous;
import com.diecolor.framework.web.domain.AjaxResult;
import com.diecolor.project.scenario.domain.TeClassInfo;
import com.diecolor.project.scenario.service.ITeClassInfoService;
import com.diecolor.project.student.domain.StLoginUser;
import com.diecolor.project.student.service.IStSysUserService;
import com.diecolor.project.student.util.StAuthUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 学生端用户Controller
 * 
 * <AUTHOR>
 */
@Slf4j
@Anonymous
@RestController
@RequestMapping("/student/user")
@RequiredArgsConstructor
public class StUserController {

    private final IStSysUserService stSysUserService;
    private final ITeClassInfoService teClassInfoService;

    /**
     * 获取当前用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/info")
    public AjaxResult getUserInfo() {
        // 检查用户是否已登录
        StLoginUser loginUser = StAuthUtil.getRequiredLoginUser();
        
        log.info("[StUserController] 获取用户 {} 的信息", loginUser.getUsername());
        
        // 构建用户信息返回对象
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("userId", loginUser.getUserId());
        userInfo.put("username", loginUser.getUsername());
        userInfo.put("nickName", loginUser.getNickname());
        userInfo.put("avatar", loginUser.getAvatar());
        userInfo.put("identifier", loginUser.getIdentifier());
        
        return AjaxResult.success("获取用户信息成功", userInfo);
    }

    /**
     * 获取当前用户的班级信息
     * 用于班级大群聊天室功能
     *
     * @return 班级信息
     */
    @GetMapping("/classInfo")
    public AjaxResult getClassInfo() {
        // 检查用户是否已登录
        StLoginUser loginUser = StAuthUtil.getRequiredLoginUser();
        
        log.info("[StUserController] 获取用户 {} 的班级信息", loginUser.getUsername());
        
        try {
            // 根据学生代码查询班级信息
            TeClassInfo classInfo = teClassInfoService.getClassInfoByStudentCode(loginUser.getUsername());
            
            if (classInfo != null && classInfo.getClassCode() != null) {
                Map<String, Object> result = new HashMap<>();
                result.put("classCode", classInfo.getClassCode());
                result.put("className", classInfo.getClassName());
                
                log.info("[StUserController] 用户 {} 的班级信息: classCode={}, className={}", 
                    loginUser.getUsername(), classInfo.getClassCode(), classInfo.getClassName());
                
                return AjaxResult.success("获取班级信息成功", result);
            }
            
            log.warn("[StUserController] 用户 {} 没有找到班级信息", loginUser.getUsername());
            return AjaxResult.error("未找到班级信息");
            
        } catch (Exception e) {
            log.error("[StUserController] 获取用户 {} 的班级信息异常", loginUser.getUsername(), e);
            return AjaxResult.error("获取班级信息失败: " + e.getMessage());
        }
    }
}