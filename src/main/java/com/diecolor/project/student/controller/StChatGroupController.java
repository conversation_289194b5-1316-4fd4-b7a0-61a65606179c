package com.diecolor.project.student.controller;

import com.diecolor.framework.aspectj.lang.annotation.Anonymous;
import com.diecolor.framework.web.domain.AjaxResult;
import com.diecolor.project.student.websocket.message.ChatMessage;
import com.diecolor.project.student.service.StChatGroupService;
import com.diecolor.project.student.service.IStSysUserService;
import com.diecolor.project.student.domain.dto.StudentCourseInfoDTO;
import com.diecolor.project.student.util.StAuthUtil;
import com.diecolor.project.student.domain.StLoginUser;
import com.diecolor.project.student.dto.GroupUserDTO;
import com.diecolor.project.system.service.ISysConfigService;
import com.diecolor.project.student.websocket.initialization.GroupInitializationService;
import com.diecolor.project.student.websocket.domain.GroupType;
import com.diecolor.project.student.websocket.domain.ChatGroup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@Anonymous
@RestController
@RequestMapping("/student/chat")
public class StChatGroupController {

    @Autowired
    private StChatGroupService chatGroupService;

    @Autowired
    private IStSysUserService stSysUserService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private GroupInitializationService groupInitializationService;

    @GetMapping("/messages/{groupId}")
    public AjaxResult getGroupMessages(
            @PathVariable String groupId,
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "50") Integer pageSize) {
        List<ChatMessage> messages = chatGroupService.getGroupMessages(groupId, pageNum, pageSize);
        return AjaxResult.success(messages);
    }

    /**
     * 获取当前登录学生的课程及分组信息
     * @return AjaxResult 包含 StudentCourseInfoDTO
     */
    @GetMapping("/currentUser/courseInfo")
    public AjaxResult getCurrentUserCourseInfo() {
        // 检查用户是否已登录，未登录会抛出401异常
        StLoginUser loginUser = StAuthUtil.getRequiredLoginUser();
        List<StudentCourseInfoDTO> courseInfo = stSysUserService.getStudentCourseInfo();
        return AjaxResult.success(courseInfo);
    }

    /**
     * 获取群组轮询配置
     * @return AjaxResult 包含轮询开关配置
     */
    @GetMapping("/config/polling")
    public AjaxResult getPollingConfig() {
        String pollingEnabled = configService.selectConfigByKey("student.group.polling.enabled");
        boolean enabled = "true".equalsIgnoreCase(pollingEnabled);
        return AjaxResult.success("获取配置成功", enabled);
    }

    /**
     * 根据课程ID获取该课程下的所有学生信息（课程大群成员）
     *
     * @param courseId 课程ID
     * @return 课程成员列表
     */
    @GetMapping("/course/{courseId}/users")
    public AjaxResult getCourseUsers(@PathVariable String courseId) {
        List<GroupUserDTO> courseUsers = chatGroupService.getCourseMembers(courseId);
        return AjaxResult.success(courseUsers);
    }

    /**
     * 根据课程分组ID获取该分组下的所有用户信息（包含马甲）
     *
     * @param groupId 课程分组ID
     * @return 分组用户信息列表
     */
    @GetMapping("/group/{groupId}/users")
    public AjaxResult getGroupUsers(@PathVariable String groupId) {
        List<GroupUserDTO> groupUsers = chatGroupService.getGroupMembers(groupId);
        return AjaxResult.success(groupUsers);
    }

    /**
     * 根据群组类型获取当前用户的群列表
     * @param type 群组类型 (CLASS-班级群, STUDY_GROUP-课程群)
     * @return AjaxResult 包含 StudentCourseInfoDTO 列表
     */
    @GetMapping("/groups/byType")
    public AjaxResult getGroupsByType(@RequestParam String type) {
        // 检查用户是否已登录
        StLoginUser loginUser = StAuthUtil.getRequiredLoginUser();
        
        // 解析群组类型
        GroupType groupType;
        try {
            groupType = GroupType.valueOf(type.toUpperCase());
        } catch (IllegalArgumentException e) {
            return AjaxResult.error("不支持的群组类型: " + type);
        }
        
        // 获取指定类型的群组
        List<ChatGroup> groups = groupInitializationService.getUserGroupsByType(
            loginUser.getUserId().toString(), groupType);
        
        // 转换为StudentCourseInfoDTO
        List<StudentCourseInfoDTO> result = convertChatGroupsToDTO(groups, loginUser);
        
        return AjaxResult.success(result);
    }

    /**
     * 将ChatGroup转换为StudentCourseInfoDTO
     */
    private List<StudentCourseInfoDTO> convertChatGroupsToDTO(List<ChatGroup> groups, StLoginUser loginUser) {
        return groups.stream().map(group -> {
            StudentCourseInfoDTO dto = new StudentCourseInfoDTO();
            dto.setUserName(loginUser.getUsername());
            dto.setNickName(loginUser.getNickname());
            dto.setAvatar(loginUser.getAvatar());
            dto.setGroupId(group.getGroupId());
            dto.setGroupName(group.getDisplayName());
            
            // 根据群组类型设置相关字段
            if (group.getGroupType() == GroupType.CLASS) {
                dto.setIsCourse(false);
                dto.setIsPublic(true); // 班级群通常是公开的
            } else if (group.getGroupType() == GroupType.STUDY_GROUP) {
                dto.setIsCourse(false);
                dto.setIsPublic(false); // 学习小组通常是私有的
            }
            
            return dto;
        }).collect(Collectors.toList());
    }
} 