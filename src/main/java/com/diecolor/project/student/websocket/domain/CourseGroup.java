package com.diecolor.project.student.websocket.domain;

import lombok.Data;

import java.util.Set;

/**
 * 课程群组实现
 */
@Data
public class CourseGroup implements ChatGroup {
    
    private final String courseId;
    private final String courseName;
    private Set<String> memberIds;
    
    public CourseGroup(String courseId, String courseName) {
        this.courseId = courseId;
        this.courseName = courseName;
    }
    
    @Override
    public String getGroupId() {
        return courseId;
    }
    
    @Override
    public GroupType getGroupType() {
        return GroupType.COURSE;
    }
    
    @Override
    public String getDisplayName() {
        return courseName;
    }
    
    @Override
    public boolean canUserJoin(String userId) {
        // TODO: 实现课程成员验证逻辑
        return true;
    }
    
    @Override
    public Set<String> getMembers() {
        return memberIds != null ? memberIds : Set.of();
    }
    
    @Override
    public Object getBusinessContext() {
        return new CourseContext(courseId, courseName);
    }
    
    /**
     * 课程业务上下文
     */
    @Data
    public static class CourseContext {
        private final String courseId;
        private final String courseName;
    }
}