package com.diecolor.project.student.websocket.initialization;

import com.diecolor.project.student.domain.dto.StudentCourseInfoDTO;
import com.diecolor.project.system.domain.SysUser;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 用户信息缓存
 * 用于批量预加载用户信息，避免在群组初始化时频繁查询数据库
 */
@Data
public class UserInfoCache {
    
    /**
     * 用户基本信息缓存
     * key: userId, value: SysUser
     */
    private Map<Long, SysUser> userInfoMap;
    
    /**
     * 用户课程信息缓存（包含奇灵信息）
     * key: userId, value: 课程信息列表
     */
    private Map<Long, List<StudentCourseInfoDTO>> userCourseInfoMap;
    
    /**
     * 构造函数
     */
    public UserInfoCache(Map<Long, SysUser> userInfoMap, 
                         Map<Long, List<StudentCourseInfoDTO>> userCourseInfoMap) {
        this.userInfoMap = userInfoMap;
        this.userCourseInfoMap = userCourseInfoMap;
    }
    
    /**
     * 获取用户基本信息
     * @param userId 用户ID
     * @return 用户信息，如果不存在返回null
     */
    public SysUser getUserInfo(Long userId) {
        return userInfoMap.get(userId);
    }
    
    /**
     * 获取用户课程信息
     * @param userId 用户ID
     * @return 用户课程信息列表，如果不存在返回空列表
     */
    public List<StudentCourseInfoDTO> getUserCourseInfo(Long userId) {
        return userCourseInfoMap.getOrDefault(userId, List.of());
    }
    
    /**
     * 获取用户昵称
     * @param userId 用户ID
     * @return 用户昵称，如果不存在返回用户ID字符串
     */
    public String getUserNickName(Long userId) {
        SysUser user = getUserInfo(userId);
        return user != null ? user.getNickName() : String.valueOf(userId);
    }
    
    /**
     * 获取用户头像
     * @param userId 用户ID
     * @return 用户头像URL，如果不存在返回空字符串
     */
    public String getUserAvatar(Long userId) {
        SysUser user = getUserInfo(userId);
        return user != null ? (user.getAvatar() != null ? user.getAvatar() : "") : "";
    }
    
    /**
     * 获取用户在指定课程/群组中的奇灵名称
     * @param userId 用户ID
     * @param groupId 群组ID
     * @return 奇灵名称，如果不存在返回用户昵称
     */
    public String getPuppetName(Long userId, String groupId) {
        List<StudentCourseInfoDTO> courseList = getUserCourseInfo(userId);
        return courseList.stream()
                .filter(course -> groupId.equals(course.getGroupId()))
                .findFirst()
                .map(StudentCourseInfoDTO::getPuppetName)
                .orElse(getUserNickName(userId));
    }
    
    /**
     * 获取用户在指定课程/群组中的奇灵头像
     * @param userId 用户ID
     * @param groupId 群组ID
     * @return 奇灵头像URL，如果不存在返回用户头像
     */
    public String getPuppetIcon(Long userId, String groupId) {
        List<StudentCourseInfoDTO> courseList = getUserCourseInfo(userId);
        return courseList.stream()
                .filter(course -> groupId.equals(course.getGroupId()))
                .findFirst()
                .map(StudentCourseInfoDTO::getPuppetIcon)
                .orElse(getUserAvatar(userId));
    }
}