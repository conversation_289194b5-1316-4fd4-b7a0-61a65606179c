package com.diecolor.project.student.websocket.domain;

import lombok.Data;

import java.util.Set;

/**
 * 班级群组实现
 */
@Data
public class ClassGroup implements ChatGroup {
    
    private final String classId;
    private final String className;
    private Set<String> memberIds;
    
    public ClassGroup(String classId, String className) {
        this.classId = classId;
        this.className = className;
    }
    
    @Override
    public String getGroupId() {
        return classId;
    }
    
    @Override
    public GroupType getGroupType() {
        return GroupType.CLASS;
    }
    
    @Override
    public String getDisplayName() {
        return className;
    }
    
    @Override
    public boolean canUserJoin(String userId) {
        return true;
    }
    
    @Override
    public Set<String> getMembers() {
        return memberIds != null ? memberIds : Set.of();
    }
    
    @Override
    public Object getBusinessContext() {
        return new ClassContext(classId, className);
    }
    
    /**
     * 班级业务上下文
     */
    @Data
    public static class ClassContext {
        private final String classId;
        private final String className;
    }
}