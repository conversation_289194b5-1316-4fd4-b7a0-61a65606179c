# 全局通知服务使用说明

## 概述

全局通知服务提供了一套完整的WebSocket通知系统，支持以下类型的通知：

1. **全局广播通知** - 向所有在线用户发送通知
2. **班级通知** - 向指定班级的所有学生发送通知
3. **课程通知** - 向指定课程的所有学生发送通知
4. **课程分组通知** - 向指定课程分组的所有学生发送通知
5. **个人通知** - 向指定用户发送通知

## 核心组件

### 1. 消息目标类型 (TargetType)

- `SYSTEM_BROADCAST` - 系统广播
- `CLASS_NOTIFICATION` - 班级通知
- `COURSE_NOTIFICATION` - 课程通知
- `COURSE_GROUP_NOTIFICATION` - 课程分组通知
- `PRIVATE` - 私人通知

### 2. 通知消息实体 (NotificationMessage)

专门用于系统通知的消息实体，包含以下字段：
- `title` - 通知标题
- `content` - 通知内容
- `level` - 通知级别 (info, warning, error, success)
- `notificationType` - 通知类型
- `messageTarget` - 消息目标
- `requireConfirmation` - 是否需要确认
- `expireTime` - 过期时间

### 3. 全局通知服务 (GlobalNotificationService)

核心服务类，提供各种通知发送方法。

## REST API 接口

### 1. 发送全局广播通知

```http
POST /student/notification/global
Content-Type: application/x-www-form-urlencoded

title=系统维护通知&content=系统将于今晚22:00进行维护&level=warning&type=maintenance
```

### 2. 发送班级通知

```http
POST /student/notification/class/{classId}
Content-Type: application/x-www-form-urlencoded

className=计算机科学与技术1班&title=课程安排变更&content=明天的数据结构课程调整到下午2点&level=info&type=schedule
```

### 3. 发送课程通知

```http
POST /student/notification/course/{courseId}
Content-Type: application/x-www-form-urlencoded

courseName=数据结构&title=作业提醒&content=请在本周五前提交第三章作业&level=info&type=homework
```

### 4. 发送课程分组通知

```http
POST /student/notification/group/{groupId}
Content-Type: application/x-www-form-urlencoded

groupName=第一小组&courseId=course123&title=小组讨论&content=请准备明天的小组讨论材料&level=info&type=discussion
```

### 5. 发送个人通知

```http
POST /student/notification/private/{userId}
Content-Type: application/x-www-form-urlencoded

userName=张三&title=成绩通知&content=您的期中考试成绩已发布&level=success&type=grade
```

## 编程接口使用示例

### 1. 注入服务

```java
@Autowired
private GlobalNotificationService notificationService;
```

### 2. 发送全局广播

```java
NotificationResult result = notificationService.sendGlobalBroadcast(
    "系统维护通知", 
    "系统将于今晚22:00-24:00进行维护，期间可能无法正常使用", 
    "warning", 
    "maintenance"
);
```

### 3. 发送班级通知

```java
NotificationResult result = notificationService.sendClassNotification(
    1001L,  // 班级ID
    "计算机科学与技术1班",  // 班级名称
    "课程安排变更", 
    "明天的数据结构课程调整到下午2点", 
    "info", 
    "schedule"
);
```

### 4. 发送课程通知

```java
NotificationResult result = notificationService.sendCourseNotification(
    "course123",  // 课程ID
    "数据结构",   // 课程名称
    "作业提醒", 
    "请在本周五前提交第三章作业", 
    "info", 
    "homework"
);
```

### 5. 发送个人通知

```java
NotificationResult result = notificationService.sendPrivateNotification(
    "user123",  // 用户ID
    "张三",     // 用户名
    "成绩通知", 
    "您的期中考试成绩已发布，请查看", 
    "success", 
    "grade"
);
```

## 通知级别说明

- `info` - 普通信息通知（蓝色）
- `success` - 成功通知（绿色）
- `warning` - 警告通知（橙色）
- `error` - 错误通知（红色）

## 通知类型建议

- `system` - 系统通知
- `maintenance` - 维护通知
- `schedule` - 课程安排
- `homework` - 作业相关
- `exam` - 考试相关
- `grade` - 成绩相关
- `discussion` - 讨论相关
- `announcement` - 公告

## 注意事项

1. 通知只会发送给当前在线的用户
2. 班级通知基于用户的部门ID (deptId) 进行发送
3. 课程通知基于课程的班级代码 (classCode) 进行发送
4. 课程分组通知基于分组成员关系进行发送
5. 所有通知都会实时通过WebSocket推送到前端
6. 通知发送结果包含成功发送的用户数量统计

## 前端接收通知

前端通过WebSocket接收通知消息，消息格式如下：

```json
{
  "id": "notification-uuid",
  "senderId": "system",
  "senderName": "系统",
  "title": "通知标题",
  "content": "通知内容",
  "level": "info",
  "notificationType": "system",
  "messageTarget": {
    "type": "SYSTEM_BROADCAST",
    "targetId": "global",
    "displayName": "全局广播"
  },
  "timestamp": "2025-01-20T10:30:00Z",
  "requireConfirmation": false
}
```
