package com.diecolor.project.student.websocket.handler;

import com.diecolor.project.student.websocket.ChatMessage;
import com.diecolor.project.student.websocket.ContentType;
import com.diecolor.project.student.websocket.MessageType;
import com.diecolor.project.student.websocket.WebSocketMessagingUtils;
import com.diecolor.project.student.websocket.support.WebSocketGroupManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;

/**
 * 处理 CHAT 类型消息的处理器（简化版）
 * 
 * 重构说明：
 * - 移除了基于群组会话映射的验证逻辑
 * - 用户可以向任何群组发送消息，无需预先加入
 * - 仅验证用户是否已认证（设置了用户名）
 * - 消息直接广播给所有在线用户
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ChatMessageHandler implements MessageHandler {

    private final WebSocketGroupManager groupManager;

    @Override
    public void handle(WebSocketSession session, ChatMessage chatMessage) throws IOException {
        // 从 GroupManager 获取会话的用户信息
        String username = chatMessage.getSender();

        // 校验用户是否已认证（仅需要设置了用户名即可）
        if (username == null) {
            log.warn("[ChatHandler] 收到来自未认证会话 {} 的 CHAT 消息。", session.getId());
            groupManager.sendMessageToSession(session, new TextMessage("{\"type\":\"ERROR\", \"content\":\"You must be authenticated before sending messages\"}"));
            return;
        }

        // 获取消息中指定的群组ID
        String targetGroupId = chatMessage.getGroupId();
        
        // 如果消息没有指定群组ID，则要求必须指定
        if (targetGroupId == null || targetGroupId.trim().isEmpty()) {
            log.warn("[ChatHandler] 收到未指定群组ID的 CHAT 消息。用户: {}, Session: {}", username, session.getId());
            groupManager.sendMessageToSession(session, new TextMessage("{\"type\":\"ERROR\", \"content\":\"GroupId is required for CHAT messages\"}"));
            return;
        }

        // 规范化消息：确保 sender, type 和 timestamp 被正确设置
        chatMessage.setSender(username); // 强制使用会话中的用户名，防止伪造
        chatMessage.setType(MessageType.CHAT); // 确保类型是 CHAT
        chatMessage.setTimestamp(java.time.Instant.now());

        // 根据内容类型处理消息
        ContentType contentType = chatMessage.getContentType();
        if (contentType == null) {
            contentType = ContentType.TEXT; // 默认为文本类型
            chatMessage.setContentType(contentType);
        }

        log.debug("[ChatHandler] 用户 '{}' 向群组 '{}' 发送 {} 类型消息: {}", 
                 username, targetGroupId, contentType, chatMessage.getContent());

        switch (contentType) {
            case TEXT:
                handleTextMessage(session, chatMessage);
                break;
            case IMAGE:
                handleImageMessage(session, chatMessage);
                break;
            case EMOJI:
                handleEmojiMessage(session, chatMessage);
                break;
            default:
                log.warn("[ChatHandler] 不支持的聊天内容类型: {}", contentType);
                groupManager.sendMessageToSession(session, 
                    new TextMessage("{\"type\":\"ERROR\", \"content\":\"不支持的聊天内容类型: " + contentType + "\"}"));
                return;
        }
    }

    /**
     * 处理文本消息
     */
    private void handleTextMessage(WebSocketSession session, ChatMessage message) {
        log.debug("[ChatHandler] 处理文本消息: {} -> 群组: {}", message.getContent(), message.getGroupId());
        // TODO: 可以在这里进行文本过滤、敏感词检查等
        groupManager.broadcastMessageToGroup(message.getGroupId(), message);
    }

    /**
     * 处理图片消息
     */
    private void handleImageMessage(WebSocketSession session, ChatMessage message) {
        log.info("[ChatHandler] 收到图片消息，暂未实现图片处理逻辑。发送者: {}, 群组: {}", 
                message.getSender(), message.getGroupId());
        // TODO: 实现图片处理逻辑
        // 1. 保存图片（可能需要生成缩略图）
        // 2. 生成图片URL
        // 3. 更新消息内容为图片URL
        // 4. 广播消息
        groupManager.broadcastMessageToGroup(message.getGroupId(), message);
    }

    /**
     * 处理Emoji消息
     */
    private void handleEmojiMessage(WebSocketSession session, ChatMessage message) {
        log.info("[ChatHandler] 收到Emoji消息，暂未实现Emoji处理逻辑。发送者: {}, 群组: {}", 
                message.getSender(), message.getGroupId());
        // TODO: 实现Emoji处理逻辑
        // 1. 验证Emoji是否有效
        // 2. 可能需要转换Emoji格式
        // 3. 广播消息
        groupManager.broadcastMessageToGroup(message.getGroupId(), message);
    }
} 