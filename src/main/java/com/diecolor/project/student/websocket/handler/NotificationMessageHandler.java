package com.diecolor.project.student.websocket.handler;

import com.diecolor.project.student.websocket.message.ChatMessage;
import com.diecolor.project.student.websocket.service.GlobalNotificationService;
import com.diecolor.project.student.websocket.support.WebSocketGroupManager;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;

/**
 * 通知消息处理器
 * 处理系统通知类型的消息
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class NotificationMessageHandler implements MessageHandler {

    private final WebSocketGroupManager groupManager;
    private final GlobalNotificationService notificationService;
    private final ObjectMapper objectMapper;

    @Override
    public void handle(WebSocketSession session, ChatMessage chatMessage) throws IOException {
        String username = groupManager.getUsername(session);
        
        // 校验用户是否已认证
        if (username == null) {
            log.warn("[通知处理器] 收到来自未认证会话 {} 的通知消息", session.getId());
            groupManager.sendMessageToSession(session, 
                new TextMessage("{\"type\":\"ERROR\", \"content\":\"You must be authenticated before sending notifications\"}"));
            return;
        }

        // 通知消息通常由系统发送，这里可以根据需要实现特定的处理逻辑
        // 例如：用户确认收到通知、标记通知为已读等
        
        log.info("[通知处理器] 收到用户 {} 的通知相关消息: {}", username, chatMessage.getContent());
        
        // 这里可以实现通知确认、已读状态更新等逻辑
        handleNotificationAcknowledgment(session, chatMessage);
    }

    @Override
    public boolean shouldStore(ChatMessage message) {
        // 通知确认消息通常不需要存储
        return false;
    }

    /**
     * 处理通知确认
     */
    private void handleNotificationAcknowledgment(WebSocketSession session, ChatMessage message) {
        try {
            // 解析消息内容，可能包含通知ID等信息
            String content = message.getContent();
            
            // 这里可以实现具体的确认逻辑
            // 例如：更新数据库中的通知状态、记录用户已读等
            
            log.debug("[通知处理器] 处理通知确认: {}", content);
            
            // 发送确认回复
            String response = "{\"type\":\"NOTIFICATION_ACK\", \"content\":\"Notification acknowledged\", \"timestamp\":\"" + 
                            java.time.Instant.now() + "\"}";
            groupManager.sendMessageToSession(session, new TextMessage(response));
            
        } catch (Exception e) {
            log.error("[通知处理器] 处理通知确认时出错", e);
        }
    }
}
