package com.diecolor.project.student.websocket.domain;

/**
 * 群组类型枚举 - 仅包含群聊相关类型
 * 私聊和系统通知将通过MessageTarget单独处理
 */
public enum GroupType {
    
    /**
     * 班级群
     */
    CLASS("班级群"),
    
    /**
     * 课程群
     */
    COURSE("课程群"),
    
    /**
     * 学习小组
     */
    STUDY_GROUP("学习小组"),
    
    /**
     * 自定义群组
     */
    CUSTOM("自定义群");
    
    private final String description;
    
    GroupType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}