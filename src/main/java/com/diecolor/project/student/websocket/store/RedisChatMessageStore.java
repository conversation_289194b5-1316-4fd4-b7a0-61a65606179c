package com.diecolor.project.student.websocket.store;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.diecolor.project.student.websocket.message.ChatMessage;
import com.diecolor.project.student.websocket.WebSocketConstants; // 引入常量类
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate; // 使用 StringRedisTemplate 更方便处理 JSON 字符串
import org.springframework.stereotype.Component;

/**
 * 使用 Redis List 实现的聊天消息异步存储服务。
 * <p>
 * 将消息序列化为 JSON 字符串并推送到 Redis List 中，
 * 由后台消费者负责将其持久化到数据库。
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RedisChatMessageStore implements ChatMessageStore {

    private final StringRedisTemplate stringRedisTemplate;
    private final ObjectMapper objectMapper;

    @Override
    public void storeMessage(ChatMessage message) {
        if (message == null) {
            log.warn("[消息存储] 尝试存储 null 消息，已忽略。");
            return;
        }
        try {
            String messageJson = objectMapper.writeValueAsString(message);
            // 使用常量 Key
            stringRedisTemplate.opsForList().leftPush(WebSocketConstants.REDIS_CHAT_MESSAGE_QUEUE_KEY, messageJson);
            log.debug("[消息存储] 消息已推送到 Redis 队列 {}: {}", WebSocketConstants.REDIS_CHAT_MESSAGE_QUEUE_KEY, messageJson);
        } catch (JsonProcessingException e) {
            log.error("[消息存储] 序列化 ChatMessage 到 JSON 时出错: {}", message, e);
            // 异常处理：可以考虑记录失败的消息到日志或死信队列
        } catch (Exception e) {
            log.error("[消息存储] 推送消息到 Redis 时发生未知错误: {}", message, e);
            // 异常处理
        }
    }
} 