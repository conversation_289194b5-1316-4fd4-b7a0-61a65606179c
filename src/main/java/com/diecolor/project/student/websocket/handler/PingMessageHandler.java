package com.diecolor.project.student.websocket.handler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.diecolor.project.student.websocket.message.ChatMessage;
import com.diecolor.project.student.websocket.message.MessageType;
import com.diecolor.project.student.websocket.support.WebSocketGroupManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.util.Map;

/**
 * 处理 PING 心跳消息的 Handler
 */
@Slf4j
@Component // 声明为 Spring Bean，以便 NativeGroupChatHandler 可以注入
public class PingMessageHandler implements MessageHandler {

    @Autowired
    private WebSocketGroupManager groupManager; // 注入 GroupManager 以便更新活跃时间

    @Autowired
    private ObjectMapper objectMapper; // 用于构建 PONG 消息

    private static final String PONG_PAYLOAD; // 预先构建 PONG 消息体

    static {
        // 静态初始化块，预先序列化 PONG 消息体，提高性能
        try {
            // PONG 消息可以很简单，只包含类型
            Map<String, String> pongMap = Map.of("type", MessageType.PONG.name());
            PONG_PAYLOAD = new ObjectMapper().writeValueAsString(pongMap);
        } catch (Exception e) {
            log.error("初始化 PONG 消息体失败", e);
            throw new RuntimeException("无法初始化 PingMessageHandler", e);
        }
    }

    /**
     * 判断此处理器处理的消息是否应该被存储。
     * 默认返回 false，需要存储的类型（如 CHAT, JOIN, LEAVE）应重写此方法返回 true。
     *
     * @param message 当前处理的消息
     * @return 如果需要存储则返回 true，否则返回 false
     */
    @Override
    public boolean shouldStore(ChatMessage message) {
        return false;
    }

    @Override
    public void handle(WebSocketSession session, ChatMessage message) throws IOException {
        if (message.getType() == MessageType.PING) {
            // 1. 更新会话的最后活跃时间（重要！用于超时检测）
            groupManager.updateSessionLastActiveTime(session);

            // 2. 回复 PONG 消息
            try {
                // 发送预先构建好的 PONG 消息
                groupManager.sendMessageToSession(session, new TextMessage(PONG_PAYLOAD));
            } catch (Exception e) {
                // 处理发送失败的情况
                log.error("[WebSocket 心跳] 向 Session {} 发送 PONG 时出错: {}", session.getId(), e.getMessage());
                // 根据策略决定是否关闭连接或记录错误
                // 例如：session.close(CloseStatus.PROTOCOL_ERROR);
            }
        } else {
            // 理论上不应该走到这里，因为 NativeGroupChatHandler 已经根据类型分发了
            log.warn("[WebSocket 心跳] PingMessageHandler 收到非 PING 类型的消息: {} from Session {}",
                     message.getType(), session.getId());
        }
    }
} 