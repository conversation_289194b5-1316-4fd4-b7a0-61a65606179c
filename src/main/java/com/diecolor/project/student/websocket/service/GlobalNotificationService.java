package com.diecolor.project.student.websocket.service;

import com.diecolor.project.student.dto.GroupUserDTO;
import com.diecolor.project.student.mapper.StSysUserMapper;
import com.diecolor.project.student.websocket.message.*;
import com.diecolor.project.student.websocket.message.targets.*;
import com.diecolor.project.student.websocket.support.WebSocketGroupManager;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.util.List;
import java.util.Map;

/**
 * 全局通知服务
 * 提供发送全局通知、班级通知、课程通知、课程分组通知、个人通知的功能
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GlobalNotificationService {

    private final WebSocketGroupManager webSocketGroupManager;
    private final StSysUserMapper stSysUserMapper;
    private final ObjectMapper objectMapper;

    /**
     * 发送全局广播通知
     * 向所有在线用户发送通知
     *
     * @param title   通知标题
     * @param content 通知内容
     * @param level   通知级别
     * @param type    通知类型
     * @return 发送结果
     */
    public NotificationResult sendGlobalBroadcast(String title, String content, NotificationLevel level, NotificationType type) {
        log.info("[全局通知服务] 发送全局广播通知: title={}, level={}, type={}", title, level, type);

        NotificationMessage notification = NotificationMessage.createGlobalBroadcast(title, content)
                .withLevel(level)
                .withType(type);

        return broadcastToAllSessions(notification);
    }

    /**
     * 发送全局广播通知（字符串版本，兼容性）
     */
    public NotificationResult sendGlobalBroadcast(String title, String content, String level, String type) {
        return sendGlobalBroadcast(title, content, NotificationLevel.fromCode(level), NotificationType.fromCode(type));
    }

    /**
     * 发送班级通知
     * 向指定班级的所有学生发送通知
     *
     * @param classCode 班级ID
     * @param className 班级名称
     * @param title     通知标题
     * @param content   通知内容
     * @param level     通知级别
     * @param type      通知类型
     * @return 发送结果
     */
    public NotificationResult sendClassNotification(String classCode, String className, String title,
                                                  String content, NotificationLevel level, NotificationType type) {
        log.info("[全局通知服务] 发送班级通知: classCode={}, className={}, title={}", classCode, className, title);

        NotificationMessage notification = NotificationMessage.createClassNotification(title, content, classCode, className)
                .withLevel(level)
                .withType(type);

        // 获取班级下的所有学生
        List<GroupUserDTO> classMembers = getClassMembers(classCode);
        if (classMembers.isEmpty()) {
            log.warn("[全局通知服务] 班级 {} 没有找到任何成员", classCode);
            return NotificationResult.failure("班级没有找到任何成员");
        }

        return sendNotificationToUsers(notification, classMembers);
    }

    /**
     * 发送班级通知（字符串版本，兼容性）
     */
    public NotificationResult sendClassNotification(Long classId, String className, String title,
                                                  String content, String level, String type) {
        return sendClassNotification(classId, className, title, content,
                                   NotificationLevel.fromCode(level), NotificationType.fromCode(type));
    }

    /**
     * 发送课程通知
     * 向指定课程的所有学生发送通知
     *
     * @param courseId   课程ID
     * @param courseName 课程名称
     * @param title      通知标题
     * @param content    通知内容
     * @param level      通知级别
     * @param type       通知类型
     * @return 发送结果
     */
    public NotificationResult sendCourseNotification(String courseId, String courseName, String title,
                                                   String content, NotificationLevel level, NotificationType type) {
        log.info("[全局通知服务] 发送课程通知: courseId={}, courseName={}, title={}", courseId, courseName, title);

        NotificationMessage notification = NotificationMessage.createCourseNotification(title, content, courseId, courseName)
                .withLevel(level)
                .withType(type);

        // 获取课程下的所有学生
        List<GroupUserDTO> courseMembers = stSysUserMapper.selectCourseMembersByCourseId(courseId);
        if (courseMembers.isEmpty()) {
            log.warn("[全局通知服务] 课程 {} 没有找到任何成员", courseId);
            return NotificationResult.failure("课程没有找到任何成员");
        }

        return sendNotificationToUsers(notification, courseMembers);
    }

    /**
     * 发送课程通知（字符串版本，兼容性）
     */
    public NotificationResult sendCourseNotification(String courseId, String courseName, String title,
                                                   String content, String level, String type) {
        return sendCourseNotification(courseId, courseName, title, content,
                                    NotificationLevel.fromCode(level), NotificationType.fromCode(type));
    }

    /**
     * 发送课程分组通知
     * 向指定课程分组的所有学生发送通知
     *
     * @param groupId    分组ID
     * @param groupName  分组名称
     * @param courseId   课程ID
     * @param title      通知标题
     * @param content    通知内容
     * @param level      通知级别
     * @param type       通知类型
     * @return 发送结果
     */
    public NotificationResult sendCourseGroupNotification(String groupId, String groupName, String courseId,
                                                        String title, String content, NotificationLevel level, NotificationType type) {
        log.info("[全局通知服务] 发送课程分组通知: groupId={}, groupName={}, title={}", groupId, groupName, title);

        NotificationMessage notification = NotificationMessage.createCourseGroupNotification(title, content, groupId, groupName, courseId)
                .withLevel(level)
                .withType(type);

        // 获取分组下的所有学生
        List<GroupUserDTO> groupMembers = stSysUserMapper.selectGroupMembersByGroupId(groupId);
        if (groupMembers.isEmpty()) {
            log.warn("[全局通知服务] 课程分组 {} 没有找到任何成员", groupId);
            return NotificationResult.failure("课程分组没有找到任何成员");
        }

        return sendNotificationToUsers(notification, groupMembers);
    }

    /**
     * 发送课程分组通知（字符串版本，兼容性）
     */
    public NotificationResult sendCourseGroupNotification(String groupId, String groupName, String courseId,
                                                        String title, String content, String level, String type) {
        return sendCourseGroupNotification(groupId, groupName, courseId, title, content,
                                         NotificationLevel.fromCode(level), NotificationType.fromCode(type));
    }

    /**
     * 发送个人通知
     * 向指定用户发送通知
     *
     * @param userId      用户ID
     * @param userName    用户名
     * @param title       通知标题
     * @param content     通知内容
     * @param level       通知级别
     * @param type        通知类型
     * @return 发送结果
     */
    public NotificationResult sendPrivateNotification(String userId, String userName, String title,
                                                    String content, NotificationLevel level, NotificationType type) {
        log.info("[全局通知服务] 发送个人通知: userId={}, userName={}, title={}", userId, userName, title);

        NotificationMessage notification = NotificationMessage.createPrivateNotification(title, content, userId, userName)
                .withLevel(level)
                .withType(type);

        // 查找用户的WebSocket会话
        WebSocketSession userSession = webSocketGroupManager.getUserSession(userName);
        if (userSession == null || !userSession.isOpen()) {
            log.warn("[全局通知服务] 用户 {} 不在线或会话已关闭", userName);
            return NotificationResult.failure("用户不在线");
        }

        return sendNotificationToSession(notification, userSession);
    }

    /**
     * 发送个人通知（字符串版本，兼容性）
     */
    public NotificationResult sendPrivateNotification(String userId, String userName, String title,
                                                    String content, String level, String type) {
        return sendPrivateNotification(userId, userName, title, content,
                                     NotificationLevel.fromCode(level), NotificationType.fromCode(type));
    }

    /**
     * 向所有在线会话广播通知
     */
    private NotificationResult broadcastToAllSessions(NotificationMessage notification) {
        try {
            String messageJson = objectMapper.writeValueAsString(notification);
            TextMessage textMessage = new TextMessage(messageJson);
            
            Map<String, WebSocketSession> allSessions = webSocketGroupManager.getAllSessions();
            int totalSessions = allSessions.size();
            int sentCount = 0;
            
            for (WebSocketSession session : allSessions.values()) {
                if (session != null && session.isOpen()) {
                    webSocketGroupManager.sendMessageToSession(session, textMessage);
                    sentCount++;
                }
            }
            
            log.info("[全局通知服务] 全局广播完成，成功发送到 {}/{} 个会话", sentCount, totalSessions);
            return NotificationResult.success(String.format("成功发送到 %d/%d 个在线用户", sentCount, totalSessions));
            
        } catch (JsonProcessingException e) {
            log.error("[全局通知服务] 序列化通知消息失败", e);
            return NotificationResult.failure("序列化消息失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("[全局通知服务] 发送全局广播失败", e);
            return NotificationResult.failure("发送失败: " + e.getMessage());
        }
    }

    /**
     * 向指定用户列表发送通知
     */
    private NotificationResult sendNotificationToUsers(NotificationMessage notification, List<GroupUserDTO> users) {
        try {
            String messageJson = objectMapper.writeValueAsString(notification);
            TextMessage textMessage = new TextMessage(messageJson);
            
            int totalUsers = users.size();
            int sentCount = 0;
            
            for (GroupUserDTO user : users) {
                WebSocketSession userSession = webSocketGroupManager.getUserSession(user.getUserName());
                if (userSession != null && userSession.isOpen()) {
                    webSocketGroupManager.sendMessageToSession(userSession, textMessage);
                    sentCount++;
                }
            }
            
            log.info("[全局通知服务] 定向通知发送完成，成功发送到 {}/{} 个用户", sentCount, totalUsers);
            return NotificationResult.success(String.format("成功发送到 %d/%d 个在线用户", sentCount, totalUsers));
            
        } catch (JsonProcessingException e) {
            log.error("[全局通知服务] 序列化通知消息失败", e);
            return NotificationResult.failure("序列化消息失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("[全局通知服务] 发送定向通知失败", e);
            return NotificationResult.failure("发送失败: " + e.getMessage());
        }
    }

    /**
     * 向单个会话发送通知
     */
    private NotificationResult sendNotificationToSession(NotificationMessage notification, WebSocketSession session) {
        try {
            String messageJson = objectMapper.writeValueAsString(notification);
            TextMessage textMessage = new TextMessage(messageJson);
            
            webSocketGroupManager.sendMessageToSession(session, textMessage);
            
            log.info("[全局通知服务] 个人通知发送成功");
            return NotificationResult.success("通知发送成功");
            
        } catch (JsonProcessingException e) {
            log.error("[全局通知服务] 序列化通知消息失败", e);
            return NotificationResult.failure("序列化消息失败: " + e.getMessage());
        } catch (Exception e) {
            log.error("[全局通知服务] 发送个人通知失败", e);
            return NotificationResult.failure("发送失败: " + e.getMessage());
        }
    }

    /**
     * 获取班级成员
     * 根据部门ID（班级ID）查询班级下的所有学生
     */
    private List<GroupUserDTO> getClassMembers(String classCode) {
        try {
            return stSysUserMapper.selectUsersByClassCode(classCode);
        } catch (Exception e) {
            log.error("[全局通知服务] 查询班级 {} 成员失败", classCode, e);
            return List.of();
        }
    }
}
