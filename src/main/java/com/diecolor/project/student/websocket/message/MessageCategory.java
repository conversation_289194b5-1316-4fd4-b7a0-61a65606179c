package com.diecolor.project.student.websocket.message;

/**
 * 消息类别枚举
 */
public enum MessageCategory {
    
    /**
     * 聊天消息
     */
    CHAT("聊天消息"),
    
    /**
     * 系统通知
     */
    SYSTEM_NOTIFY("系统通知"),
    
    /**
     * 控制消息，如JOIN/LEAVE
     */
    CONTROL("控制消息");
    
    private final String description;
    
    MessageCategory(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}