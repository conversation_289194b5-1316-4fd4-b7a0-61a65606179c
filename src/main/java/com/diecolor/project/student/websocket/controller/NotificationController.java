package com.diecolor.project.student.websocket.controller;

import com.diecolor.framework.web.domain.AjaxResult;
import com.diecolor.project.student.websocket.service.GlobalNotificationService;
import com.diecolor.project.student.websocket.service.NotificationResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 通知服务REST API控制器
 * 提供发送各种类型通知的接口
 */
@Slf4j
@RestController
@RequestMapping("/student/notification")
@RequiredArgsConstructor
@Api(tags = "通知服务")
public class NotificationController {

    private final GlobalNotificationService notificationService;

    /**
     * 发送全局广播通知
     */
    @PostMapping("/global")
    @ApiOperation("发送全局广播通知")
    public AjaxResult sendGlobalBroadcast(
            @ApiParam("通知标题") @RequestParam String title,
            @ApiParam("通知内容") @RequestParam String content,
            @ApiParam("通知级别") @RequestParam(defaultValue = "info") String level,
            @ApiParam("通知类型") @RequestParam(defaultValue = "system") String type) {
        
        try {
            NotificationResult result = notificationService.sendGlobalBroadcast(title, content, level, type);
            if (result.isSuccess()) {
                return AjaxResult.success(result.getMessage(), result);
            } else {
                return AjaxResult.error(result.getMessage());
            }
        } catch (Exception e) {
            log.error("[通知控制器] 发送全局广播失败", e);
            return AjaxResult.error("发送全局广播失败: " + e.getMessage());
        }
    }

    /**
     * 发送班级通知
     */
    @PostMapping("/class/{classId}")
    @ApiOperation("发送班级通知")
    public AjaxResult sendClassNotification(
            @ApiParam("班级ID") @PathVariable Long classId,
            @ApiParam("班级名称") @RequestParam(required = false) String className,
            @ApiParam("通知标题") @RequestParam String title,
            @ApiParam("通知内容") @RequestParam String content,
            @ApiParam("通知级别") @RequestParam(defaultValue = "info") String level,
            @ApiParam("通知类型") @RequestParam(defaultValue = "class") String type) {
        
        try {
            NotificationResult result = notificationService.sendClassNotification(
                    classId, className, title, content, level, type);
            if (result.isSuccess()) {
                return AjaxResult.success(result.getMessage(), result);
            } else {
                return AjaxResult.error(result.getMessage());
            }
        } catch (Exception e) {
            log.error("[通知控制器] 发送班级通知失败", e);
            return AjaxResult.error("发送班级通知失败: " + e.getMessage());
        }
    }

    /**
     * 发送课程通知
     */
    @PostMapping("/course/{courseId}")
    @ApiOperation("发送课程通知")
    public AjaxResult sendCourseNotification(
            @ApiParam("课程ID") @PathVariable String courseId,
            @ApiParam("课程名称") @RequestParam(required = false) String courseName,
            @ApiParam("通知标题") @RequestParam String title,
            @ApiParam("通知内容") @RequestParam String content,
            @ApiParam("通知级别") @RequestParam(defaultValue = "info") String level,
            @ApiParam("通知类型") @RequestParam(defaultValue = "course") String type) {
        
        try {
            NotificationResult result = notificationService.sendCourseNotification(
                courseId, courseName, title, content, level, type);
            if (result.isSuccess()) {
                return AjaxResult.success(result.getMessage(), result);
            } else {
                return AjaxResult.error(result.getMessage());
            }
        } catch (Exception e) {
            log.error("[通知控制器] 发送课程通知失败", e);
            return AjaxResult.error("发送课程通知失败: " + e.getMessage());
        }
    }

    /**
     * 发送课程分组通知
     */
    @PostMapping("/group/{groupId}")
    @ApiOperation("发送课程分组通知")
    public AjaxResult sendCourseGroupNotification(
            @ApiParam("分组ID") @PathVariable String groupId,
            @ApiParam("分组名称") @RequestParam(required = false) String groupName,
            @ApiParam("课程ID") @RequestParam String courseId,
            @ApiParam("通知标题") @RequestParam String title,
            @ApiParam("通知内容") @RequestParam String content,
            @ApiParam("通知级别") @RequestParam(defaultValue = "info") String level,
            @ApiParam("通知类型") @RequestParam(defaultValue = "group") String type) {
        
        try {
            NotificationResult result = notificationService.sendCourseGroupNotification(
                groupId, groupName, courseId, title, content, level, type);
            if (result.isSuccess()) {
                return AjaxResult.success(result.getMessage(), result);
            } else {
                return AjaxResult.error(result.getMessage());
            }
        } catch (Exception e) {
            log.error("[通知控制器] 发送课程分组通知失败", e);
            return AjaxResult.error("发送课程分组通知失败: " + e.getMessage());
        }
    }

    /**
     * 发送个人通知
     */
    @PostMapping("/private/{userId}")
    @ApiOperation("发送个人通知")
    public AjaxResult sendPrivateNotification(
            @ApiParam("用户ID") @PathVariable String userId,
            @ApiParam("用户名") @RequestParam(required = false) String userName,
            @ApiParam("通知标题") @RequestParam String title,
            @ApiParam("通知内容") @RequestParam String content,
            @ApiParam("通知级别") @RequestParam(defaultValue = "info") String level,
            @ApiParam("通知类型") @RequestParam(defaultValue = "private") String type) {
        
        try {
            NotificationResult result = notificationService.sendPrivateNotification(
                userId, userName, title, content, level, type);
            if (result.isSuccess()) {
                return AjaxResult.success(result.getMessage(), result);
            } else {
                return AjaxResult.error(result.getMessage());
            }
        } catch (Exception e) {
            log.error("[通知控制器] 发送个人通知失败", e);
            return AjaxResult.error("发送个人通知失败: " + e.getMessage());
        }
    }
}
