package com.diecolor.project.student.websocket.message;

/**
 * 消息目标类型枚举
 */
public enum TargetType {
    
    /**
     * 群聊消息
     */
    GROUP("群聊"),
    
    /**
     * 私聊消息
     */
    PRIVATE("私聊"),
    
    /**
     * 系统广播通知
     */
    SYSTEM_BROADCAST("系统广播"),
    
    /**
     * 定向系统通知
     */
    SYSTEM_TARGETED("定向通知");
    
    private final String description;
    
    TargetType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}