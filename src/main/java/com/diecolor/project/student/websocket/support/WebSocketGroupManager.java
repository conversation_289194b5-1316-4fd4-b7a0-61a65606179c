package com.diecolor.project.student.websocket.support;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.diecolor.project.student.websocket.message.ChatMessage;
import com.diecolor.project.student.websocket.vo.GroupUserInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.CloseStatus;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import static com.diecolor.project.student.websocket.WebSocketConstants.ATTR_GROUP_ID;
import static com.diecolor.project.student.websocket.WebSocketConstants.ATTR_USERNAME;

/**
 * WebSocket 群组和会话管理器（简化版）
 * 
 * 重构说明：
 * - 移除了基于群组的会话映射（groupSessions）
 * - 采用基于用户权限的广播机制
 * - 每个用户维持一个WebSocket连接，可以向任何有权限的群组发送消息
 * - JOIN/LEAVE消息仅用于业务通知，不影响实际的消息路由
 */
@Slf4j
@Component
@RequiredArgsConstructor
@EnableScheduling // 启用定时任务
public class WebSocketGroupManager {

    private final ObjectMapper objectMapper;
    // Redis 用户连接状态管理器
    private final RedisUserConnectionManager redisUserConnectionManager;

    // 按会话 ID 存储会话
    private final Map<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    // 按用户ID存储会话（便于根据用户ID查找会话）
    private final Map<String, WebSocketSession> userSessions = new ConcurrentHashMap<>();
    // 存储会话最后活跃时间 (Session ID -> Timestamp ms)
    private final Map<String, Long> sessionLastActiveTime = new ConcurrentHashMap<>();

    // 心跳超时时间（例如 90 秒）
    private static final long HEARTBEAT_TIMEOUT_MS = TimeUnit.SECONDS.toMillis(90);
    // 定时任务检查间隔（例如 30 秒），值用于 @Scheduled
    private static final long TIMEOUT_CHECK_INTERVAL_MS_VALUE = 30000; // 30 * 1000

    /**
     * 添加新的 WebSocket 会话。
     * 同时记录初始活跃时间。
     *
     * @param session 新的会话
     */
    public void addSession(WebSocketSession session) {
        sessions.put(session.getId(), session);
        sessionLastActiveTime.put(session.getId(), System.currentTimeMillis()); // 记录初始时间
        log.debug("[群组管理器] WebSocket 连接已建立: Session ID = {}, Remote Address = {}",
                 session.getId(), session.getRemoteAddress());
    }

    /**
     * 移除 WebSocket 会话。
     * 同时移除活跃时间记录和用户会话映射。
     *
     * @param session 要移除的会话
     * @return 如果会话被移除，则返回 true；否则返回 false
     */
    public boolean removeSession(WebSocketSession session) {
        sessionLastActiveTime.remove(session.getId()); // 移除时间记录
        boolean removed = sessions.remove(session.getId()) != null;
        
        // 从用户会话映射中移除
        String username = getUsername(session);
        if (username != null) {
            userSessions.remove(username);
            log.debug("[群组管理器] 用户 '{}' 的会话映射已清理", username);
        }
        
        if (removed) {
            log.info("[群组管理器] WebSocket 连接已关闭: Session ID = {}", session.getId());
        }
        return removed;
    }

    /**
     * 更新会话的最后活跃时间。
     * 在收到 PING 或其他有效消息时调用。
     *
     * @param session 要更新的会话
     */
    public void updateSessionLastActiveTime(WebSocketSession session) {
        if (session != null && session.isOpen() && sessions.containsKey(session.getId())) {
             sessionLastActiveTime.put(session.getId(), System.currentTimeMillis());
             log.trace("[群组管理器] 更新会话 {} 的最后活跃时间", session.getId());
        } else if (session != null){
             log.warn("[群组管理器] 尝试更新一个 null、已关闭或未管理的会话 {} 的活跃时间，已忽略。", session.getId());
        }
    }

    /**
     * 定时任务：检查并关闭超时的 WebSocket 会话。
     */
    @Scheduled(fixedRate = TIMEOUT_CHECK_INTERVAL_MS_VALUE) // 使用常量值
    public void checkSessionTimeouts() {
        long now = System.currentTimeMillis();
//        log.debug("[群组管理器-心跳检查] 开始检查 {} 个会话的超时情况...", sessions.size());

        // 使用 entrySet() 获取快照，避免并发修改问题
        for (Map.Entry<String, WebSocketSession> entry : sessions.entrySet()) {
            String sessionId = entry.getKey();
            WebSocketSession session = entry.getValue();
            
            try {
                // 检查会话是否仍然存在于 lastActiveTime 映射中
                Long lastActive = sessionLastActiveTime.get(sessionId);

                if (lastActive == null) {
                    // 如果会话存在于 sessions 但不在 lastActiveTime 中（理论上不应发生，除非有并发问题或逻辑错误）
                    log.warn("[群组管理器-心跳检查] 会话 {} 缺少最后活跃时间记录，可能需要手动处理或关闭。", sessionId);
                    continue; // 跳过当前会话
                }

                if ((now - lastActive) > HEARTBEAT_TIMEOUT_MS) {
                    log.warn("[群组管理器-心跳检查] 会话 {} 超时 (最后活跃时间: {} ms 前)。正在关闭...",
                             sessionId, (now - lastActive));
                    closeSessionWithError(session, CloseStatus.SESSION_NOT_RELIABLE.withReason("Heartbeat timeout"));
                }
            } catch (Exception e) {
                // 捕获所有异常，确保一个会话的错误不会影响其他会话的检查
                log.error("[群组管理器-心跳检查] 检查会话 {} 时发生异常: {}", sessionId, e.getMessage(), e);
                // 可选：对于发生异常的会话，可以考虑关闭它
                try {
                    closeSessionWithError(session, CloseStatus.SERVER_ERROR.withReason("Error during timeout check"));
                } catch (Exception ex) {
                    log.error("[群组管理器-心跳检查] 关闭异常会话 {} 时发生错误: {}", sessionId, ex.getMessage(), ex);
                }
            }
        }
//        log.debug("[群组管理器-心跳检查] 检查完成。");
    }

    /**
     * 设置用户会话信息（用于JOIN消息处理）。
     * 这个方法主要用于兼容现有的JOIN逻辑，但不再维护群组会话映射。
     *
     * @param session  要设置的会话
     * @param username 用户名
     * @param groupId  当前群组 ID（用于兼容，但不影响消息路由）
     */
    public void setUserSession(WebSocketSession session, String username, String groupId) {
        // 更新会话属性
        session.getAttributes().put(ATTR_USERNAME, username);
        session.getAttributes().put(ATTR_GROUP_ID, groupId);
        
        // 建立用户名到会话的映射
        userSessions.put(username, session);
        
        log.info("[群组管理器] 用户 '{}' 会话已设置，当前查看群组: '{}'. Session ID: {}", 
                username, groupId, session.getId());
    }

    /**
     * 清除用户会话信息（用于LEAVE消息处理）。
     * 这个方法主要用于兼容现有的LEAVE逻辑。
     *
     * @param session 要清除的会话
     */
    public void clearUserSession(WebSocketSession session) {
        String username = getUsername(session);
        
        // 清理会话属性
        session.getAttributes().remove(ATTR_USERNAME);
        session.getAttributes().remove(ATTR_GROUP_ID);
        
        // 从用户会话映射中移除
        if (username != null) {
            userSessions.remove(username);
        }
        
        log.info("[群组管理器] 会话用户信息已清除. Session ID: {}", session.getId());
    }

    /**
     * 向指定群组广播消息（优化版）。
     * 只向群组内的用户广播消息，而不是向所有在线用户广播。
     * 同时也向监控端广播消息，便于大屏端实时监控。
     *
     * @param groupId 群组 ID
     * @param message 要广播的消息
     */
    public void broadcastMessageToGroup(String groupId, ChatMessage message) {
        if (sessions.isEmpty()) {
            log.warn("[群组管理器] 无法广播消息，当前没有活动的会话。群组: '{}'", groupId);
            return;
        }

        try {
            // 获取群组内的所有用户
            List<GroupUserInfo> groupUsers = redisUserConnectionManager.getGroupUsers(groupId);
            if (groupUsers.isEmpty()) {
                log.warn("[群组管理器] 群组 '{}' 中没有在线用户，跳过广播", groupId);
                return;
            }

            String messageJson = objectMapper.writeValueAsString(message);
            TextMessage textMessage = new TextMessage(messageJson);
            String messageType = message.getType() != null ? message.getType().name() : "UNKNOWN";

            log.debug("[群组管理器] 向群组 '{}' 的 {} 个用户广播 {} 消息: {}", 
                     groupId, groupUsers.size(), messageType, messageJson);

            int sentCount = 0;
            int monitorSentCount = 0;
            int targetUserCount = groupUsers.size();

            // 向群组内的用户广播消息
            for (GroupUserInfo groupUser : groupUsers) {
                String sessionId = groupUser.getSessionId();
                if (sessionId != null) {
                    WebSocketSession session = sessions.get(sessionId);
                    if (session != null && session.isOpen()) {
                        sendMessageToSession(session, textMessage);
                        sentCount++;
                    } else {
                        log.debug("[群组管理器] 用户 {} 的会话 {} 不存在或已关闭，跳过广播", 
                                 groupUser.getUserId(), sessionId);
                        // 可选：清理无效的会话ID
                        // redisUserConnectionManager.userLeaveGroup(groupId, groupUser.getUserId());
                    }
                }
            }
            
            // 向所有监控端会话广播消息
            for (WebSocketSession session : sessions.values()) {
                if (session != null && session.isOpen()) {
                    String username = getUsername(session);
                    if ("monitor".equals(username)) {
                        sendMessageToSession(session, textMessage);
                        monitorSentCount++;
                        log.trace("[群组管理器] 向监控端会话 {} 发送群组 '{}' 的消息", 
                                session.getId(), groupId);
                    }
                }
            }
            
            log.debug("[群组管理器] 群组 '{}' 消息广播完成，成功发送到 {}/{} 个群组用户，{} 个监控端", 
                     groupId, sentCount, targetUserCount, monitorSentCount);
                     
        } catch (JsonProcessingException e) {
            log.error("[群组管理器] 序列化广播消息时出错: {}", message, e);
        } catch (Exception e) {
            log.error("[群组管理器] 向群组 '{}' 广播消息时出错: {}", groupId, e.getMessage(), e);
        }
    }

    /**
     * 向单个 WebSocket 会话安全地发送消息。
     *
     * @param session     目标会话
     * @param textMessage 要发送的文本消息
     */
    public void sendMessageToSession(WebSocketSession session, TextMessage textMessage) {
        // 检查会话是否仍然打开
         if (session == null || !session.isOpen()) {
             log.warn("[群组管理器] 尝试向一个 null 或已关闭的会话发送消息 (ID: {}), 跳过。", 
                     session != null ? session.getId() : "null");
             return;
         }

        try {
            // 每个会话的发送操作进行同步，防止并发写入问题
            synchronized (session) {
                // 再次检查，以防在进入同步块之前关闭
                if (session.isOpen()) {
                    session.sendMessage(textMessage);
                    log.trace("[群组管理器] 成功发送消息到会话 {}", session.getId());
                } else {
                    log.warn("[群组管理器] 在同步块内发现会话 {} 已关闭，取消发送。", session.getId());
                }
            }
        } catch (IOException e) {
            log.error("[群组管理器] 发送消息到会话 {} 失败: {}", session.getId(), e.getMessage(), e);
        } catch (IllegalStateException ex) {
             log.warn("[群组管理器] 在广播期间会话 {} 状态非法 (可能正在关闭): {}", session.getId(), ex.getMessage());
        }
    }

    /**
     * 检查会话是否已设置用户信息。
     *
     * @param session 要检查的会话
     * @return 如果会话已关联用户名，则返回 true
     */
    public boolean isSessionAuthenticated(WebSocketSession session) {
        return session.getAttributes().containsKey(ATTR_USERNAME);
    }

    /**
     * 获取会话关联的用户名。
     *
     * @param session 会话
     * @return 用户名，如果未设置则返回 null
     */
    public String getUsername(WebSocketSession session) {
        return (String) session.getAttributes().get(ATTR_USERNAME);
    }

    /**
     * 获取会话关联的当前群组 ID。
     * 注意：这个群组ID仅表示用户当前查看的群组，不影响消息路由。
     *
     * @param session 会话
     * @return 群组 ID，如果未设置则返回 null
     */
    public String getGroupId(WebSocketSession session) {
        return (String) session.getAttributes().get(ATTR_GROUP_ID);
    }

    /**
     * 根据用户名获取会话。
     *
     * @param username 用户名
     * @return 会话，如果用户不在线则返回 null
     */
    public WebSocketSession getSessionByUsername(String username) {
        return userSessions.get(username);
    }

    /**
     * 根据用户名获取用户会话（别名方法，用于通知服务）
     *
     * @param username 用户名
     * @return 会话，如果用户不在线则返回 null
     */
    public WebSocketSession getUserSession(String username) {
        return getSessionByUsername(username);
    }

    /**
     * 获取所有会话的映射
     *
     * @return 所有会话的映射（sessionId -> WebSocketSession）
     */
    public Map<String, WebSocketSession> getAllSessions() {
        return new HashMap<>(sessions);
    }

    /**
     * 获取当前在线用户数量。
     *
     * @return 在线用户数量
     */
    public int getOnlineUserCount() {
        return userSessions.size();
    }

    /**
     * （辅助方法）安全地关闭会话并记录错误。
     *
     * @param session 要关闭的会话
     * @param reason  关闭原因状态
     */
     private void closeSessionWithError(WebSocketSession session, CloseStatus reason) {
         if (session != null && session.isOpen()) {
             try {
                  log.warn("[群组管理器] 因错误关闭会话 {}: {}", session.getId(), reason);
                  // 从活跃时间映射中移除，防止重复处理
                  sessionLastActiveTime.remove(session.getId());
                  session.close(reason);
             } catch (IOException e) {
                 log.error("[群组管理器] 关闭会话 {} 时发生 IO 异常: {}", session.getId(), e.getMessage());
                 // 即使关闭失败，也尝试从管理器中移除
                 cleanupSessionData(session);
             } catch(Exception ex){
                  log.error("[群组管理器] 关闭会话 {} 时发生意外错误: {}", session.getId(), ex.getMessage(), ex);
                  cleanupSessionData(session);
             }
         } else if (session != null) {
              log.debug("[群组管理器] 尝试关闭一个已经关闭的会话 {}，进行清理...", session.getId());
              cleanupSessionData(session);
         }
     }

     /**
      * 清理会话相关数据（用于关闭异常时）
      */
     private void cleanupSessionData(WebSocketSession session) {
         if (session == null) return;
         String sessionId = session.getId();
         String username = getUsername(session);
         
         sessions.remove(sessionId); // 从主会话列表移除
         sessionLastActiveTime.remove(sessionId); // 确保活跃时间移除
         
         // 从用户会话映射中移除
         if (username != null) {
             userSessions.remove(username);
         }
         
         log.warn("[群组管理器] 已尝试清理会话 {} 的残留数据。", sessionId);
     }
} 