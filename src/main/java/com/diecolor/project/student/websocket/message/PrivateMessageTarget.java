package com.diecolor.project.student.websocket.message;

import lombok.Data;

/**
 * 私聊消息目标
 */
@Data
public class PrivateMessageTarget implements MessageTarget {
    
    private final String recipientId;
    private final String recipientName;
    
    public PrivateMessageTarget(String recipientId, String recipientName) {
        this.recipientId = recipientId;
        this.recipientName = recipientName;
    }
    
    public PrivateMessageTarget(String recipientId) {
        this(recipientId, null);
    }
    
    @Override
    public TargetType getType() {
        return TargetType.PRIVATE;
    }
    
    @Override
    public String getTargetId() {
        return recipientId;
    }
    
    @Override
    public String getDisplayName() {
        return recipientName != null ? recipientName : "用户" + recipientId;
    }
}