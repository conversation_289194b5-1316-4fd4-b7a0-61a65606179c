package com.diecolor.project.student.websocket.handler;

import com.diecolor.project.student.websocket.message.ChatMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;

/**
 * WebSocket 消息处理器接口
 */
public interface MessageHandler {

    /**
     * 处理消息
     *
     * @param session WebSocket 会话
     * @param message 聊天消息
     * @throws IOException IO 异常
     */
    void handle(WebSocketSession session, ChatMessage message) throws IOException;

    /**
     * 判断此处理器处理的消息是否应该被存储。
     * 默认返回 true。
     *
     * @param message 当前处理的消息
     * @return 如果需要存储则返回 true，否则返回 false
     */
    default boolean shouldStore(ChatMessage message) {
        return true; // 默认存储
    }

} 