package com.diecolor.project.student.websocket.handler;

import com.diecolor.project.student.websocket.ChatMessage;
import com.diecolor.project.student.websocket.MessageType;
import com.diecolor.project.student.websocket.WebSocketMessagingUtils;
import com.diecolor.project.student.websocket.support.WebSocketGroupManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;

/**
 * 处理 LEAVE 类型消息的处理器（简化版）
 * 
 * 重构说明：
 * - LEAVE消息现在主要用于业务通知，不影响实际的消息路由
 * - 用户仍然可以向所有有权限的群组发送消息
 * - 仅清除当前查看群组的会话信息
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LeaveMessageHandler implements MessageHandler {

    private final WebSocketGroupManager groupManager;

    @Override
    public void handle(WebSocketSession session, ChatMessage leaveMessage) throws IOException {
        // 从 GroupManager 获取会话的当前状态
        String username = groupManager.getUsername(session);
        String groupId = groupManager.getGroupId(session);

        // 校验用户是否已设置会话信息
        if (username == null) {
            log.warn("[LeaveHandler] 收到来自未认证会话 {} 的 LEAVE 消息，忽略。", session.getId());
            groupManager.sendMessageToSession(session, new TextMessage("{\"type\":\"INFO\", \"content\":\"You are not currently authenticated.\"}"));
            return;
        }

        // 如果没有指定groupId，使用会话中的当前群组
        if (groupId == null) {
            groupId = leaveMessage.getGroupId();
        }
        
        if (groupId == null) {
            log.warn("[LeaveHandler] LEAVE消息缺少群组ID，忽略。Session ID: {}", session.getId());
            groupManager.sendMessageToSession(session, new TextMessage("{\"type\":\"ERROR\", \"content\":\"GroupId is required for LEAVE\"}"));
            return;
        }

        log.info("[LeaveHandler] 用户 '{}' 请求离开群组 '{}'. Session ID: {}", username, groupId, session.getId());

        // 准备并广播 LEAVE 消息（在清除会话信息之前）
        ChatMessage broadcastLeaveMsg = new ChatMessage();
        broadcastLeaveMsg.setType(MessageType.LEAVE);
        broadcastLeaveMsg.setSender(username);
        broadcastLeaveMsg.setGroupId(groupId);
        broadcastLeaveMsg.setContent(username + " 离开了群聊");
        broadcastLeaveMsg.setTimestamp(java.time.Instant.now());

        // 广播离开消息给所有用户
        groupManager.broadcastMessageToGroup(groupId, broadcastLeaveMsg);

        // 清除用户会话信息（主要是清除当前查看的群组）
        // 注意：用户仍然可以向其他群组发送消息，这只是清除了"当前查看"状态
        groupManager.clearUserSession(session);

        log.debug("[LeaveHandler] 用户 '{}' LEAVE消息处理完成，已清除当前查看群组状态", username);
    }
} 