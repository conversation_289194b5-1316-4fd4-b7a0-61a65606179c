package com.diecolor.project.student.websocket.domain;

import lombok.Data;

import java.util.Set;

/**
 * 学习小组群组实现
 */
@Data
public class StudyGroup implements ChatGroup {
    
    private final String groupId;
    private final String groupName;
    private final String courseId;
    private Set<String> memberIds;
    
    public StudyGroup(String groupId, String groupName, String courseId) {
        this.groupId = groupId;
        this.groupName = groupName;
        this.courseId = courseId;
    }
    
    @Override
    public String getGroupId() {
        return groupId;
    }
    
    @Override
    public GroupType getGroupType() {
        return GroupType.STUDY_GROUP;
    }
    
    @Override
    public String getDisplayName() {
        return groupName;
    }
    
    @Override
    public boolean canUserJoin(String userId) {
        // TODO: 实现学习小组成员验证逻辑
        return true;
    }
    
    @Override
    public Set<String> getMembers() {
        return memberIds != null ? memberIds : Set.of();
    }
    
    @Override
    public Object getBusinessContext() {
        return new StudyGroupContext(groupId, groupName, courseId);
    }
    
    /**
     * 学习小组业务上下文
     */
    @Data
    public static class StudyGroupContext {
        private final String groupId;
        private final String groupName;
        private final String courseId;
    }
}