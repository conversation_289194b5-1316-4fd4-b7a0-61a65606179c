package com.diecolor.project.student.websocket.message.targets;

import com.diecolor.project.student.websocket.message.MessageTarget;
import com.diecolor.project.student.websocket.message.TargetType;
import lombok.Data;

/**
 * 系统广播消息目标
 * 用于全局广播通知
 */
@Data
public class SystemBroadcastTarget implements MessageTarget {
    
    private final String broadcastId;
    private final String broadcastName;
    
    public SystemBroadcastTarget() {
        this("global", "全局广播");
    }
    
    public SystemBroadcastTarget(String broadcastId, String broadcastName) {
        this.broadcastId = broadcastId;
        this.broadcastName = broadcastName;
    }
    
    @Override
    public TargetType getType() {
        return TargetType.SYSTEM_BROADCAST;
    }
    
    @Override
    public String getTargetId() {
        return broadcastId;
    }
    
    @Override
    public String getDisplayName() {
        return broadcastName;
    }
}
