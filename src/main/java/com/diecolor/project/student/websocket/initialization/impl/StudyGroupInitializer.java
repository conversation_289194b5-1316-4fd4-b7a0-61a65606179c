package com.diecolor.project.student.websocket.initialization.impl;

import com.diecolor.project.student.domain.dto.StudentCourseInfoDTO;
import com.diecolor.project.student.service.IStSysUserService;
import com.diecolor.project.student.util.PuppetIconUtil;
import com.diecolor.project.student.websocket.config.ChatRoomProperties;
import com.diecolor.project.student.websocket.domain.ChatGroup;
import com.diecolor.project.student.websocket.domain.GroupType;
import com.diecolor.project.student.websocket.domain.StudyGroup;
import com.diecolor.project.student.websocket.initialization.GroupInitializer;
import com.diecolor.project.student.websocket.support.RedisUserConnectionManager;
import com.diecolor.project.system.domain.SysUser;
import com.diecolor.project.system.mapper.SysUserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketSession;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 学习小组群组初始化器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StudyGroupInitializer implements GroupInitializer {
    
    private final ChatRoomProperties chatRoomProperties;
    private final RedisUserConnectionManager redisUserConnectionManager;
    private final IStSysUserService stSysUserService;
    private final SysUserMapper sysUserMapper;
    private final PuppetIconUtil puppetIconUtil;

    @Override
    public GroupType getSupportedType() {
        return GroupType.STUDY_GROUP;
    }
    
    @Override
    public List<ChatGroup> initializeUserGroups(String userId) {
        List<StudentCourseInfoDTO> courseInfo = stSysUserService.getStudentCourseInfo(Long.parseLong(userId));
         return courseInfo.stream()
                 .map(groupInfo -> new StudyGroup(
                         groupInfo.getGroupId(),
                         groupInfo.getGroupName(),
                         groupInfo.getCourseId()
                 ))
                 .collect(Collectors.toList());
    }
    
    @Override
    public boolean isEnabled() {
        return chatRoomProperties.getStudyGroup().isEnabled();
    }
    
    @Override
    public int getPriority() {
        return 20; // 学习小组优先级中等
    }
    
    @Override
    public void joinUserToGroupCache(Long userId, ChatGroup group, WebSocketSession session, com.diecolor.project.student.websocket.initialization.UserInfoCache userInfoCache) {
        if (!(group instanceof StudyGroup)) {
            log.warn("StudyGroupInitializer 收到非学习小组: {}", group.getClass().getSimpleName());
            return;
        }
        
        StudyGroup studyGroup = (StudyGroup) group;
        StudyGroup.StudyGroupContext context = (StudyGroup.StudyGroupContext) studyGroup.getBusinessContext();
        
        // 使用缓存获取用户信息，避免数据库查询
        String userName = getUserDisplayNameFromCache(userId, studyGroup.getGroupId(), userInfoCache);
        String userAvatar = getUserAvatarFromCache(userId, studyGroup.getGroupId(), userInfoCache);
        
        redisUserConnectionManager.userJoinGroup(
            studyGroup.getGroupId(),
            userId,
            userName,
            studyGroup.getDisplayName(),
            userAvatar,
            session.getId()
        );
        
        log.info("[学习小组缓存] 用户 {} 已成功加入学习小组 {} ({}) - 课程: {}",
                userId, studyGroup.getGroupId(), studyGroup.getDisplayName(), context.getCourseId());
    }
    
    /**
     * 从缓存获取用户在学习小组中的显示名称（奇灵名称）
     */
    private String getUserDisplayNameFromCache(Long userId, String groupId, com.diecolor.project.student.websocket.initialization.UserInfoCache userInfoCache) {
        String puppetName = userInfoCache.getPuppetName(userId, groupId);
        return puppetName != null ? puppetName : userInfoCache.getUserNickName(userId);
    }
    
    /**
     * 从缓存获取用户头像（奇灵头像或用户头像）
     */
    private String getUserAvatarFromCache(Long userId, String groupId, com.diecolor.project.student.websocket.initialization.UserInfoCache userInfoCache) {
        String puppetIcon = userInfoCache.getPuppetIcon(userId, groupId);
        if (puppetIcon != null && !puppetIcon.isEmpty()) {
            return puppetIconUtil.buildIconUrlSafely(puppetIcon);
        }
        return userInfoCache.getUserAvatar(userId);
    }
}