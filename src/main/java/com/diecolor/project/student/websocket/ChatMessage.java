package com.diecolor.project.student.websocket;

import com.diecolor.project.student.websocket.domain.ChatGroup;
import com.diecolor.project.student.websocket.domain.StudyGroup;
import com.diecolor.project.student.websocket.message.BaseMessage;
import com.diecolor.project.student.websocket.message.MessageCategory;
import com.diecolor.project.student.websocket.message.MessageTarget;
import com.diecolor.project.student.websocket.message.GroupMessageTarget;
import com.diecolor.project.student.websocket.message.PrivateMessageTarget;
import com.diecolor.project.student.websocket.message.TargetType;
import lombok.Data;
import lombok.NonNull;

import java.time.Instant;
import java.util.UUID;

@Data
public class ChatMessage implements BaseMessage {
    /**
     * 消息ID
     */
    private String id;
    
    /**
     * 消息类型：CHAT, JOIN, LEAVE, REGISTER (保持向后兼容)
     */
    private MessageType type;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 发送者ID
     */
    private String sender;
    
    /**
     * 发送者昵称
     */
    private String nickname;
    
    /**
     * 发送者头像
     */
    private String avatar;
    
    /**
     * 新版消息目标（推荐使用）
     */
    private MessageTarget messageTarget;
    
    /**
     * 旧版聊天目标（向后兼容，已弃用）
     * @deprecated 使用 messageTarget 替代
     */
    @Deprecated
    private Object target; // 简化为Object类型
    
    /**
     * 群组ID（向后兼容，已弃用）
     * @deprecated 使用 messageTarget 替代
     */
    @Deprecated
    private String groupId;
    
    /**
     * 课程ID（向后兼容，已弃用）
     * @deprecated 使用 messageTarget 替代
     */
    @Deprecated
    private String courseId;
    
    /**
     * 时间戳
     */
    private Instant timestamp;
    
    /**
     * 消息内容类型：TEXT, IMAGE, EMOJI
     * 仅当 type 为 CHAT 时有效
     */
    private ContentType contentType = ContentType.TEXT;
    
    /**
     * 构造函数
     */
    public ChatMessage() {
        this.id = UUID.randomUUID().toString();
        this.timestamp = Instant.now();
    }
    
    /**
     * 新版构造函数（推荐使用）
     */
    public ChatMessage(@NonNull String sender, @NonNull String content, @NonNull MessageTarget messageTarget) {
        this();
        this.sender = sender;
        this.content = content;
        this.messageTarget = messageTarget;
        this.type = MessageType.CHAT;
    }
    
    /**
     * 旧版构造函数（向后兼容）
     * @deprecated 使用新版构造函数
     */
    @Deprecated
    public ChatMessage(@NonNull String sender, @NonNull String content, @NonNull String groupId) {
        this();
        this.sender = sender;
        this.content = content;
        this.groupId = groupId; // 简化为直接设置群组ID
        this.type = MessageType.CHAT;
    }
    
    // BaseMessage 接口实现
    @Override
    public String getId() {
        return id;
    }
    
    @Override
    public MessageCategory getCategory() {
        if (type == null) {
            return MessageCategory.CHAT;
        }
        switch (type) {
            case CHAT:
                return MessageCategory.CHAT;
            case JOIN:
            case LEAVE:
            case SYSTEM:
                return MessageCategory.CONTROL;
            case ERROR:
            case PING:
            case PONG:
                return MessageCategory.SYSTEM_NOTIFY;
            default:
                return MessageCategory.CHAT;
        }
    }
    
    @Override
    public String getSenderId() {
        return sender;
    }
    
    @Override
    public String getContent() {
        return content;
    }
    
    @Override
    public Instant getTimestamp() {
        return timestamp;
    }
    
    // 新版工厂方法（推荐使用）
    /**
     * 创建群聊消息
     */
    public static ChatMessage createGroupMessage(String senderId, String content, ChatGroup group) {
        GroupMessageTarget target = new GroupMessageTarget(group);
        return new ChatMessage(senderId, content, target);
    }
    
    /**
     * 创建私聊消息
     */
    public static ChatMessage createPrivateMessage(String senderId, String content, String recipientId) {
        PrivateMessageTarget target = new PrivateMessageTarget(recipientId);
        return new ChatMessage(senderId, content, target);
    }
    
    /**
     * 创建私聊消息（带接收者名称）
     */
    public static ChatMessage createPrivateMessage(String senderId, String content, String recipientId, String recipientName) {
        PrivateMessageTarget target = new PrivateMessageTarget(recipientId, recipientName);
        return new ChatMessage(senderId, content, target);
    }
    
    // 向后兼容的工厂方法
    /**
     * 创建群聊消息（向后兼容）
     * @deprecated 使用 createGroupMessage(String, String, ChatGroup) 替代
     */
    @Deprecated
    public static ChatMessage createGroupMessage(String senderId, String content, String groupId) {
        ChatMessage message = new ChatMessage(senderId, content, groupId);
        message.setType(MessageType.CHAT);
        return message;
    }
    
    // 向后兼容方法
    /**
     * 获取群组ID（向后兼容）
     * 优先从新版messageTarget获取，如果为空则从旧版字段获取
     */
    public String getGroupId() {
        // 优先使用新版messageTarget
        if (messageTarget != null && messageTarget.getType() == TargetType.GROUP) {
            return messageTarget.getTargetId();
        }
        
        // 兼容旧版target（已简化）
        // target现在是Object类型，不再处理
        
        // 最后使用直接字段
        return groupId;
    }
    
    /**
     * 设置群组ID（向后兼容）
     * @deprecated 使用 setMessageTarget(GroupMessageTarget) 替代
     */
    @Deprecated
    public void setGroupId(String groupId) {
        this.groupId = groupId;
        
        // 简化实现，只设置群组ID字段
        // 不再维护旧版target对象
    }
    
    /**
     * 获取课程ID（向后兼容）
     * 优先从新版messageTarget的业务上下文获取
     */
    public String getCourseId() {
        // 从新版messageTarget获取
        if (messageTarget instanceof GroupMessageTarget) {
            GroupMessageTarget groupTarget = (GroupMessageTarget) messageTarget;
            Object context = groupTarget.getGroup().getBusinessContext();
            if (context instanceof StudyGroup.StudyGroupContext) {
                return ((StudyGroup.StudyGroupContext) context).getCourseId();
            }
        }
        
        // 旧版target已简化，不再处理
        
        // 最后使用直接字段
        return courseId;
    }
    
    /**
     * 设置课程ID（向后兼容）
     * @deprecated 使用适当的GroupMessageTarget替代
     */
    @Deprecated
    public void setCourseId(String courseId) {
        this.courseId = courseId;
        
        // 简化实现，只设置课程ID字段
        // 不再维护旧版target对象
    }
    
    /**
     * 获取消息目标的显示名称
     * @return 目标显示名称
     */
    public String getTargetDisplayName() {
        if (messageTarget != null) {
            return messageTarget.getDisplayName();
        }
        return null;
    }
    
    /**
     * 判断是否为群聊消息
     * @return 是否为群聊
     */
    public boolean isGroupMessage() {
        if (messageTarget != null) {
            return messageTarget.getType() == TargetType.GROUP;
        }
        return groupId != null;
    }
    
    /**
     * 判断是否为私聊消息
     * @return 是否为私聊
     */
    public boolean isPrivateMessage() {
        if (messageTarget != null) {
            return messageTarget.getType() == TargetType.PRIVATE;
        }
        return false; // 旧版私聊类型已不支持，返回false
    }
} 