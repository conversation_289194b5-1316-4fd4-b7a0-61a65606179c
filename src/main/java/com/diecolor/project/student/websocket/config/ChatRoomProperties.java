package com.diecolor.project.student.websocket.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 聊天室配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "chat.room")
public class ChatRoomProperties {
    
    /**
     * 班级聊天室配置
     */
    private ClassRoom classRoom = new ClassRoom();
    
    /**
     * 学习小组聊天室配置
     */
    private StudyGroup studyGroup = new StudyGroup();
    
    /**
     * 课程聊天室配置
     */
    private Course course = new Course();
    
    /**
     * 班级聊天室配置
     */
    @Data
    public static class ClassRoom {
        /**
         * 是否启用班级聊天室，默认启用
         */
        private boolean enabled = true;
    }
    
    /**
     * 学习小组聊天室配置
     */
    @Data
    public static class StudyGroup {
        /**
         * 是否启用学习小组聊天室，默认启用
         */
        private boolean enabled = true;
    }
    
    /**
     * 课程聊天室配置
     */
    @Data
    public static class Course {
        /**
         * 是否启用课程聊天室，默认启用
         */
        private boolean enabled = true;
    }
}