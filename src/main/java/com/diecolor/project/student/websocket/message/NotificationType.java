package com.diecolor.project.student.websocket.message;

/**
 * 通知类型枚举
 */
public enum NotificationType {
    
    /**
     * 系统通知
     */
    SYSTEM("system", "系统通知"),
    
    /**
     * 维护通知
     */
    MAINTENANCE("maintenance", "维护通知"),
    
    /**
     * 课程安排
     */
    SCHEDULE("schedule", "课程安排"),
    
    /**
     * 作业相关
     */
    HOMEWORK("homework", "作业相关"),
    
    /**
     * 考试相关
     */
    EXAM("exam", "考试相关"),
    
    /**
     * 成绩相关
     */
    GRADE("grade", "成绩相关"),
    
    /**
     * 讨论相关
     */
    DISCUSSION("discussion", "讨论相关"),
    
    /**
     * 公告
     */
    ANNOUNCEMENT("announcement", "公告"),
    
    /**
     * 班级通知
     */
    CLASS("class", "班级通知"),
    
    /**
     * 课程通知
     */
    COURSE("course", "课程通知"),
    
    /**
     * 分组通知
     */
    GROUP("group", "分组通知"),
    
    /**
     * 个人通知
     */
    PRIVATE("private", "个人通知"),
    
    /**
     * 其他
     */
    OTHER("other", "其他");
    
    private final String code;
    private final String description;
    
    NotificationType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举值
     */
    public static NotificationType fromCode(String code) {
        if (code == null) {
            return SYSTEM; // 默认值
        }
        for (NotificationType type : values()) {
            if (type.code.equalsIgnoreCase(code)) {
                return type;
            }
        }
        return SYSTEM; // 默认值
    }
}
