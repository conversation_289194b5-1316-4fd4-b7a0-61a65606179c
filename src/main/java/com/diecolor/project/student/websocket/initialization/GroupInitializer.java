package com.diecolor.project.student.websocket.initialization;

import com.diecolor.project.student.websocket.domain.ChatGroup;
import com.diecolor.project.student.websocket.domain.GroupType;
import org.springframework.web.socket.WebSocketSession;

import java.util.List;

/**
 * 群组初始化器接口 - 方案1轻量级设计
 * 用于初始化用户可以访问的群组并管理缓存
 */
public interface GroupInitializer {
    
    /**
     * 获取支持的群组类型
     * @return 群组类型
     */
    GroupType getSupportedType();
    
    /**
     * 初始化用户的群组列表
     * @param userId 用户ID
     * @return 用户可以访问的群组列表
     */
    List<ChatGroup> initializeUserGroups(String userId);
    
    /**
     * 将用户加入到群组缓存中
     * 每个初始化器负责处理自己类型群组的缓存逻辑
     * @param userId 用户ID
     * @param group 群组对象
     * @param session WebSocket会话
     * @param userInfoCache 用户信息缓存，避免重复查询数据库
     */
    void joinUserToGroupCache(Long userId, ChatGroup group, WebSocketSession session, UserInfoCache userInfoCache);
    
    /**
     * 是否启用该初始化器
     * @return 是否启用，默认为true
     */
    default boolean isEnabled() {
        return true;
    }
    
    /**
     * 获取初始化器优先级
     * 数值越小优先级越高
     * @return 优先级，默认为100
     */
    default int getPriority() {
        return 100;
    }
}