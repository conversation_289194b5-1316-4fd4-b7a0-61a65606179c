package com.diecolor.project.student.websocket.initialization.impl;

import com.diecolor.project.student.websocket.config.ChatRoomProperties;
import com.diecolor.project.student.websocket.domain.ChatGroup;
import com.diecolor.project.student.websocket.domain.CourseGroup;
import com.diecolor.project.student.websocket.domain.GroupType;
import com.diecolor.project.student.websocket.initialization.GroupInitializer;
import com.diecolor.project.student.websocket.support.RedisUserConnectionManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketSession;

import java.util.Collections;
import java.util.List;

/**
 * 课程群组初始化器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CourseGroupInitializer implements GroupInitializer {
    
    private final ChatRoomProperties chatRoomProperties;
    private final RedisUserConnectionManager redisUserConnectionManager;
    
    @Override
    public GroupType getSupportedType() {
        return GroupType.COURSE;
    }
    
    @Override
    public List<ChatGroup> initializeUserGroups(String userId) {
        log.debug("返回空列表，业务上去掉了");
        return Collections.emptyList();
    }
    
    @Override
    public boolean isEnabled() {
        return chatRoomProperties.getCourse().isEnabled();
    }
    
    @Override
    public int getPriority() {
        return 5; // 课程群优先级最高
    }
    
    @Override
    public void joinUserToGroupCache(Long userId, ChatGroup group, WebSocketSession session, com.diecolor.project.student.websocket.initialization.UserInfoCache userInfoCache) {
        if (!(group instanceof CourseGroup)) {
            log.warn("CourseGroupInitializer 收到非课程群组: {}", group.getClass().getSimpleName());
            return;
        }
        
        CourseGroup courseGroup = (CourseGroup) group;
        
        // 使用缓存获取用户信息，避免数据库查询
        String userName = userInfoCache.getUserNickName(userId);
        String userAvatar = userInfoCache.getUserAvatar(userId);
        
        redisUserConnectionManager.userJoinGroup(
            courseGroup.getGroupId(),
            userId,
            userName,
            courseGroup.getDisplayName(),
            userAvatar,
            session.getId()
        );
        
        log.info("[课程群缓存] 用户 {} 已成功加入课程群 {} ({})",
                userId, courseGroup.getGroupId(), courseGroup.getDisplayName());
    }
}