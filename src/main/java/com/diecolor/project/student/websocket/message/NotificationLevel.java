package com.diecolor.project.student.websocket.message;

/**
 * 通知级别枚举
 */
public enum NotificationLevel {
    
    /**
     * 普通信息通知（蓝色）
     */
    INFO("info", "信息"),
    
    /**
     * 成功通知（绿色）
     */
    SUCCESS("success", "成功"),
    
    /**
     * 警告通知（橙色）
     */
    WARNING("warning", "警告"),
    
    /**
     * 错误通知（红色）
     */
    ERROR("error", "错误");
    
    private final String code;
    private final String description;
    
    NotificationLevel(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举值
     */
    public static NotificationLevel fromCode(String code) {
        if (code == null) {
            return INFO; // 默认值
        }
        for (NotificationLevel level : values()) {
            if (level.code.equalsIgnoreCase(code)) {
                return level;
            }
        }
        return INFO; // 默认值
    }
}
