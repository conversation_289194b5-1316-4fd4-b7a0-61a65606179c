package com.diecolor.project.student.websocket.initialization;

import com.diecolor.project.student.domain.dto.StudentCourseInfoDTO;
import com.diecolor.project.student.service.IStSysUserService;
import com.diecolor.project.student.websocket.domain.ChatGroup;
import com.diecolor.project.student.websocket.domain.GroupType;
import com.diecolor.project.system.domain.SysUser;
import com.diecolor.project.system.mapper.SysUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.socket.WebSocketSession;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 群组初始化服务 - 方案1轻量级实现
 */
@Slf4j
@Service
public class GroupInitializationService {
    
    @Autowired
    private List<GroupInitializer> initializers;
    
    @Autowired
    private SysUserMapper sysUserMapper;
    
    @Autowired
    private IStSysUserService stSysUserService;
    
    /**
     * 初始化用户的所有群组
     * @param userId 用户ID
     * @return 所有可访问的群组列表
     */
    public List<ChatGroup> initializeAllUserGroups(String userId) {
        log.debug("开始初始化用户群组, userId: {}", userId);
        
        List<ChatGroup> allGroups = initializers.stream()
                .filter(GroupInitializer::isEnabled)
                .sorted(Comparator.comparingInt(GroupInitializer::getPriority))
                .flatMap(initializer -> {
                    try {
                        List<ChatGroup> groups = initializer.initializeUserGroups(userId);
                        log.debug("初始化器 {} 为用户 {} 创建了 {} 个群组", 
                                initializer.getClass().getSimpleName(), userId, groups.size());
                        return groups.stream();
                    } catch (Exception e) {
                        log.error("初始化器 {} 执行失败", initializer.getClass().getSimpleName(), e);
                        return java.util.stream.Stream.empty();
                    }
                })
                .collect(Collectors.toList());
        
        log.info("用户 {} 共初始化了 {} 个群组", userId, allGroups.size());
        return allGroups;
    }
    
    /**
     * 按类型获取用户群组
     * @param userId 用户ID
     * @param groupType 群组类型
     * @return 指定类型的群组列表
     */
    public List<ChatGroup> getUserGroupsByType(String userId, GroupType groupType) {
        return initializers.stream()
                .filter(GroupInitializer::isEnabled)
                .filter(init -> init.getSupportedType() == groupType)
                .flatMap(init -> init.initializeUserGroups(userId).stream())
                .collect(Collectors.toList());
    }
    
    /**
     * 获取用户群组按类型分组的映射
     * @param userId 用户ID
     * @return 按类型分组的群组映射
     */
    public Map<GroupType, List<ChatGroup>> getUserGroupsGroupedByType(String userId) {
        return initializeAllUserGroups(userId).stream()
                .collect(Collectors.groupingBy(ChatGroup::getGroupType));
    }
    
    /**
     * 将用户加入到所有初始化的群组缓存中
     * 使用各自的初始化器处理缓存逻辑，优化了数据库查询性能
     * @param userId 用户ID
     * @param groups 群组列表
     * @param session WebSocket会话
     * @return 成功加入的群组数量
     */
    public int joinUserToAllGroupCache(Long userId, List<ChatGroup> groups, WebSocketSession session) {
        log.debug("开始将用户 {} 加入到 {} 个群组缓存中", userId, groups.size());
        
        // 批量预加载用户信息，避免N+1查询
        UserInfoCache userInfoCache = preloadUserInfo(userId);
        
        int successCount = 0;
        for (ChatGroup group : groups) {
            try {
                // 查找对应的初始化器处理缓存加入
                GroupInitializer targetInitializer = findInitializerForGroup(group);
                if (targetInitializer != null) {
                    targetInitializer.joinUserToGroupCache(userId, group, session, userInfoCache);
                    successCount++;
                } else {
                    log.warn("找不到群组 {} 对应的初始化器", group.getGroupId());
                }
            } catch (Exception e) {
                log.error("用户 {} 加入群组 {} 缓存失败", userId, group.getGroupId(), e);
            }
        }
        
        log.info("用户 {} 成功加入 {} 个群组缓存", userId, successCount);
        return successCount;
    }
    
    /**
     * 预加载用户信息，批量查询避免N+1问题
     * @param userId 用户ID
     * @return 用户信息缓存
     */
    private UserInfoCache preloadUserInfo(Long userId) {
        try {
            // 批量查询用户基本信息
            Map<Long, SysUser> userInfoMap = new HashMap<>();
            SysUser userInfo = sysUserMapper.selectUserById(userId);
            if (userInfo != null) {
                userInfoMap.put(userId, userInfo);
            }
            
            // 批量查询用户课程信息
            Map<Long, List<StudentCourseInfoDTO>> userCourseInfoMap = new HashMap<>();
            List<StudentCourseInfoDTO> courseInfo = stSysUserService.getStudentCourseInfo(userId);
            userCourseInfoMap.put(userId, courseInfo);
            
            log.debug("预加载用户 {} 信息完成：基本信息 {} 条，课程信息 {} 条", 
                    userId, userInfoMap.size(), courseInfo.size());
            
            return new UserInfoCache(userInfoMap, userCourseInfoMap);
            
        } catch (Exception e) {
            log.error("预加载用户 {} 信息失败", userId, e);
            // 返回空缓存，降级处理
            return new UserInfoCache(new HashMap<>(), new HashMap<>());
        }
    }
    
    /**
     * 查找群组对应的初始化器
     * @param group 群组
     * @return 对应的初始化器，如果未找到返回null
     */
    private GroupInitializer findInitializerForGroup(ChatGroup group) {
        return initializers.stream()
                .filter(GroupInitializer::isEnabled)
                .filter(init -> init.getSupportedType() == group.getGroupType())
                .findFirst()
                .orElse(null);
    }
}