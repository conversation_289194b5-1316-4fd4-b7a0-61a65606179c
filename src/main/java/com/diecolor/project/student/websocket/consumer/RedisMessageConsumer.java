package com.diecolor.project.student.websocket.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.diecolor.project.scenario.domain.DcCourseChatHistory;
import com.diecolor.project.scenario.service.IDcCourseChatHistoryService;
import com.diecolor.project.student.websocket.ChatMessage;
import com.diecolor.project.student.websocket.WebSocketConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 从 Redis List 消费聊天消息并记录日志的后台任务。
 * <p>
 * 使用非阻塞 RPOP 轮询模式，在一个单独的线程中运行。
 * TODO: 后续可以考虑替换为 Redis Streams 以获得更完整的 MQ 功能。
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RedisMessageConsumer implements InitializingBean, DisposableBean {

    // 非阻塞轮询间隔（毫秒），当队列为空时等待时间
    private static final long POLL_INTERVAL_MS = 100;

    private final StringRedisTemplate stringRedisTemplate;
    private final ObjectMapper objectMapper;
    private final IDcCourseChatHistoryService dcCourseChatHistoryService;
    @Resource(name = "threadPoolTaskExecutor")
    private final ThreadPoolTaskExecutor taskExecutor;

    private final AtomicBoolean running = new AtomicBoolean(false);
    private volatile Thread consumerThread;

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("[消息消费] 初始化 Redis 消息消费者...");
        this.running.set(true);
        taskExecutor.execute(this::runConsumerLoop);
        log.info("[消息消费] Redis 消息消费者已启动，监听队列: {}", WebSocketConstants.REDIS_CHAT_MESSAGE_QUEUE_KEY);
    }

    private void runConsumerLoop() {
        this.consumerThread = Thread.currentThread();
        log.info("[消息消费] 消费者循环开始于线程: {}", this.consumerThread.getName());

        while (running.get() && !Thread.currentThread().isInterrupted()) {
            try {
                // 检查Redis连接状态
                if (!isRedisConnectionHealthy()) {
                    log.warn("[消息消费] Redis连接不健康，等待恢复...");
                    TimeUnit.SECONDS.sleep(3);
                    continue;
                }
                
                String messageJson = stringRedisTemplate.opsForList().rightPop(
                        WebSocketConstants.REDIS_CHAT_MESSAGE_QUEUE_KEY
                );

                if (messageJson != null) {
                    log.debug("[消息消费] 从 Redis 队列 {} (RPOP) 收到消息: {}", WebSocketConstants.REDIS_CHAT_MESSAGE_QUEUE_KEY, messageJson);
                    processMessage(messageJson);
                } else {
                    // 无消息时短暂等待，避免空转占用CPU
                    if (running.get()) {
                        TimeUnit.MILLISECONDS.sleep(POLL_INTERVAL_MS);
                    }
                }
            } catch (Exception e) {
                if (running.get()) {
                    // 所有Redis操作异常都视为可恢复的错误，进行统一处理
                    log.debug("[消息消费] Redis 操作异常: {} - {}，等待后继续监听...", e.getClass().getSimpleName(), e.getMessage());
                    try {
                        TimeUnit.SECONDS.sleep(2);
                    } catch (InterruptedException ie) {
                        log.warn("[消息消费] 消费者线程在异常恢复等待时被中断，准备退出...");
                        Thread.currentThread().interrupt();
                    }
                } else {
                    log.info("[消息消费] 消费者线程收到退出信号，在 RPOP 时捕获到异常，正常退出。 ({})", e.getMessage());
                }
            }
        }
        log.info("[消息消费] 消费者循环结束于线程: {}", Thread.currentThread().getName());
    }

    private void processMessage(String messageJson) {
        try {
            ChatMessage message = objectMapper.readValue(messageJson, ChatMessage.class);

            // --- 持久化逻辑 ---
            DcCourseChatHistory chatHistory = new DcCourseChatHistory();
            chatHistory.setIdNumber(message.getSender());
            chatHistory.setGroupId(message.getGroupId());
            chatHistory.setCourseId(message.getCourseId());
            
            // 保存完整的消息JSON到chatContent字段
            chatHistory.setChatContent(messageJson);
            
            // 解析时间戳字符串为Date对象
            Date chatTime = parseChatTime(message.getTimestamp());
            chatHistory.setChatTime(chatTime);
            
            // 保存到数据库
            boolean saved = dcCourseChatHistoryService.save(chatHistory);
            if (saved) {
                log.info("[消息消费] 成功保存聊天消息到数据库: ChatId={}, Sender={}, GroupId={}, CourseId={}, Type={}, JsonLength={}",
                         chatHistory.getChatId(), message.getSender(), message.getGroupId(), message.getCourseId(), message.getType(), messageJson.length());
            } else {
                log.error("[消息消费] 保存聊天消息到数据库失败: Sender={}, GroupId={}, CourseId={}, Content={}",
                          message.getSender(), message.getGroupId(), message.getCourseId(), message.getContent());
            }
            // --- 持久化逻辑结束 ---

        } catch (JsonProcessingException e) {
            log.error("[消息消费] 反序列化 Redis 消息时出错: {}", messageJson, e);
            // 异常处理：考虑将无法解析的消息移到死信队列
        } catch (Exception e) {
            log.error("[消息消费] 处理消息时发生意外错误: {}", messageJson, e);
            // 异常处理
        }
    }
    
    /**
     * 解析聊天时间戳为Date对象
     * 
     * @param timestamp Instant时间戳
     * @return Date对象，解析失败时返回当前时间
     */
    private Date parseChatTime(java.time.Instant timestamp) {
        if (timestamp == null) {
            return new Date();
        }
        
        try {
            // 将Instant转换为Date
            Date date = Date.from(timestamp);
            
            // 检查时间是否在合理范围内 (1970-2099年)
            long time = date.getTime();
            long minTime = 0L; // 1970-01-01
            long maxTime = 4102444800000L; // 2099-12-31
            
            if (time < minTime || time > maxTime) {
                log.warn("[消息消费] 时间戳超出合理范围: {}, 使用当前时间", timestamp);
                return new Date();
            }
            
            return date;
        } catch (Exception e) {
            log.warn("[消息消费] 解析时间戳时发生异常: {}, 使用当前时间", timestamp, e);
            return new Date();
        }
    }

    @Override
    public void destroy() throws Exception {
        log.info("[消息消费] 开始停止 Redis 消息消费者...");
        this.running.set(false);
        if (consumerThread != null) {
            log.info("[消息消费] 中断消费者线程: {}", consumerThread.getName());
            consumerThread.interrupt();
        }
        log.info("[消息消费] Redis 消息消费者已停止。");
    }

    /**
     * 检查Redis连接是否健康
     */
    private boolean isRedisConnectionHealthy() {
        try {
            stringRedisTemplate.opsForValue().get("_health_check_");
            return true;
        } catch (Exception e) {
            log.debug("[消息消费] Redis连接健康检查失败: {}", e.getMessage());
            return false;
        }
    }
} 