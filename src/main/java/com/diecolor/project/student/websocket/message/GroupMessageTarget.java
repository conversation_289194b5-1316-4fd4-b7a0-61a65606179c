package com.diecolor.project.student.websocket.message;

import com.diecolor.project.student.websocket.domain.ChatGroup;
import lombok.Data;

/**
 * 群聊消息目标
 */
@Data
public class GroupMessageTarget implements MessageTarget {
    
    private final ChatGroup group;
    
    public GroupMessageTarget(ChatGroup group) {
        this.group = group;
    }
    
    @Override
    public TargetType getType() {
        return TargetType.GROUP;
    }
    
    @Override
    public String getTargetId() {
        return group.getGroupId();
    }
    
    @Override
    public String getDisplayName() {
        return group.getDisplayName();
    }
    
    /**
     * 获取群组对象
     * @return 群组
     */
    public ChatGroup getGroup() {
        return group;
    }
}