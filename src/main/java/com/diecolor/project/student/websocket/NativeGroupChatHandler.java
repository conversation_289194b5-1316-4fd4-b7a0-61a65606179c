package com.diecolor.project.student.websocket;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.diecolor.project.student.websocket.handler.MessageHandler;
import com.diecolor.project.student.websocket.store.ChatMessageStore;
import com.diecolor.project.student.websocket.support.RedisUserConnectionManager;
import com.diecolor.project.student.websocket.support.WebSocketGroupManager;
import com.diecolor.project.student.websocket.vo.GroupUserInfo;
import com.diecolor.project.student.domain.dto.StudentCourseInfoDTO;
import com.diecolor.project.student.service.IStSysUserService;
import com.diecolor.project.student.websocket.domain.ChatGroup;
import com.diecolor.project.student.websocket.initialization.GroupInitializationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 原生 WebSocket 群聊处理器 (重构版)
 * <p>
 * 该处理器负责基本的 WebSocket 连接生命周期管理和消息分发。
 * 实际的业务逻辑（如加入/离开群组、处理聊天消息）委托给具体的 {@link MessageHandler} 实现。
 * 会话和群组管理委托给 {@link WebSocketGroupManager}。
 * 用户连接状态的 Redis 缓存管理委托给 {@link RedisUserConnectionManager}。
 */
@Slf4j
@Component
public class NativeGroupChatHandler extends TextWebSocketHandler {

    // 会话和群组管理器
    private final WebSocketGroupManager groupManager;
    // JSON 序列化/反序列化工具
    private final ObjectMapper objectMapper;
    // 消息类型到具体处理器的映射
    private final Map<MessageType, MessageHandler> messageHandlers;
    // 消息存储服务
    private final ChatMessageStore chatMessageStore;
    // Redis 用户连接状态管理器
    private final RedisUserConnectionManager redisUserConnectionManager;
    // 学生用户服务，用于查询用户所在的群组
    private final IStSysUserService stSysUserService;
    // 群组初始化服务（新版）
    private final GroupInitializationService groupInitializationService;

    @Autowired
    public NativeGroupChatHandler(WebSocketGroupManager groupManager,
                                  ObjectMapper objectMapper,
                                  Map<String, MessageHandler> handlers, // Spring 会自动注入所有 MessageHandler bean
                                  ChatMessageStore chatMessageStore,
                                  RedisUserConnectionManager redisUserConnectionManager,
                                  IStSysUserService stSysUserService,
                                  GroupInitializationService groupInitializationService) {
        this.groupManager = groupManager;
        this.objectMapper = objectMapper;
        this.chatMessageStore = chatMessageStore;
        this.redisUserConnectionManager = redisUserConnectionManager;
        this.stSysUserService = stSysUserService;
        this.groupInitializationService = groupInitializationService;
        this.messageHandlers = new EnumMap<>(MessageType.class);

        // 从 Spring 注入的 Map<String, MessageHandler> 构建 EnumMap<MessageType, MessageHandler>
        handlers.forEach((beanName, handler) -> {
            try {
                // 尝试将 beanName 转换为 MessageType 枚举常量
                String typeName = beanName.replace("MessageHandler", "").toUpperCase();
                MessageType messageType = MessageType.valueOf(typeName);
                this.messageHandlers.put(messageType, handler);
                log.info("已注册消息处理器: {} -> {}", messageType, handler.getClass().getSimpleName());
            } catch (IllegalArgumentException e) {
                log.warn("无法将 Bean 名称 '{}' 映射到 MessageType 枚举。跳过处理器: {}",
                         beanName, handler.getClass().getSimpleName());
            }
        });

        // 验证是否所有核心类型都有处理器
        if (!messageHandlers.containsKey(MessageType.JOIN)) {
            log.error("缺少 JOIN 类型的消息处理器!");
        }
        if (!messageHandlers.containsKey(MessageType.LEAVE)) {
            log.error("缺少 LEAVE 类型的消息处理器!");
        }
        if (!messageHandlers.containsKey(MessageType.CHAT)) {
            log.error("缺少 CHAT 类型的消息处理器!");
        }
    }

    /**
     * WebSocket 连接建立后调用。
     * 将新会话添加到管理器中，并查询用户所在的群组进行缓存。
     */
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        groupManager.addSession(session);
        log.debug("[WebSocket 主处理器] 用户连接建立: Session ID = {}", session.getId());
        
        // 尝试从URL参数中获取用户信息
        String userId = extractUserIdFromSession(session);
        if (userId != null) {
            try {
                // 查询用户所在的所有群组
                initializeUserGroupsCache(userId, session);
                // 标记会话为已初始化
                markSessionAsInitialized(session);
            } catch (Exception e) {
                log.error("[WebSocket 主处理器] 初始化用户群组缓存失败: Session {}, UserId: {}", 
                         session.getId(), userId, e);
            }
        } else {
            log.debug("[WebSocket 主处理器] 未能从连接中提取用户ID，将等待后续操作确定用户身份: Session ID = {}", 
                     session.getId());
        }
    }

    /**
     * 从WebSocket会话中提取用户ID
     * 支持从URL参数、请求头等方式获取
     */
    private String extractUserIdFromSession(WebSocketSession session) {
        try {
            // 方法1：从 URI 查询参数中获取 userId
            String query = session.getUri().getQuery();
            log.debug("[WebSocket 主处理器] Session {} URI查询参数: {}", session.getId(), query);
            
            if (query != null && !query.trim().isEmpty()) {
                String[] params = query.split("&");
                for (String param : params) {
                    if (param.startsWith("userId=")) {
                        String userId = param.substring("userId=".length());
                        log.info("[WebSocket 主处理器] 从URI参数成功提取用户ID: {} (Session: {})", userId, session.getId());
                        return userId;
                    }
                }
                log.debug("[WebSocket 主处理器] URI参数中未找到userId字段 (Session: {})", session.getId());
            } else {
                log.debug("[WebSocket 主处理器] URI查询参数为空 (Session: {})", session.getId());
            }

            // 方法2：从会话属性中获取（如果之前已设置）
            String userIdFromAttr = (String) session.getAttributes().get("userId");
            if (userIdFromAttr != null) {
                log.info("[WebSocket 主处理器] 从会话属性成功提取用户ID: {} (Session: {})", userIdFromAttr, session.getId());
                return userIdFromAttr;
            } else {
                log.debug("[WebSocket 主处理器] 会话属性中未找到userId (Session: {})", session.getId());
            }
            
            log.warn("[WebSocket 主处理器] 无法从任何途径提取用户ID (Session: {}, URI: {})", 
                    session.getId(), session.getUri());
            return null;
        } catch (Exception e) {
            log.error("[WebSocket 主处理器] 提取用户ID时发生错误: Session {}, URI: {}", 
                     session.getId(), session.getUri(), e);
            return null;
        }
    }

    /**
     * 初始化用户群组缓存 (重构版 - 双轨实现)
     * 优先使用新版群组初始化服务，如果失败则退回到旧版实现
     */
    private void initializeUserGroupsCache(String userIdStr, WebSocketSession session) {
        // 尝试使用新版群组初始化服务
        if (initializeUserGroupsCacheV2(userIdStr, session)) {
            return; // 新版初始化成功
        }
        
        // 新版初始化失败，使用旧版实现
        log.warn("[新版初始化] 用户 {} 新版群组初始化失败，退回到旧版实现", userIdStr);
        initializeUserGroupsCacheLegacy(userIdStr, session);
    }
    
    /**
     * 新版群组初始化实现
     * 使用GroupInitializationService进行群组初始化
     */
    private boolean initializeUserGroupsCacheV2(String userIdStr, WebSocketSession session) {
        try {
            // 检查是否为监控端连接
            if ("monitor".equals(userIdStr)) {
                log.info("[新版初始化] 监控端连接，设置会话用户名: Session ID = {}", session.getId());
                groupManager.setUserSession(session, "monitor", null);
                return true;
            }
            
            log.info("[新版初始化] 开始初始化用户 {} 的群组缓存, Session ID = {}", userIdStr, session.getId());
            
            // 使用新版群组初始化服务
            List<ChatGroup> userGroups = groupInitializationService.initializeAllUserGroups(userIdStr);
            
            if (userGroups.isEmpty()) {
                log.warn("[新版初始化] 用户 {} 没有任何群组，缓存初始化中止。", userIdStr);
                return true; // 这不算失败，只是用户没有群组
            }
            
            // 将用户设置为在线状态
            Long userId = Long.parseLong(userIdStr);
            redisUserConnectionManager.userOnline(userId);
            log.debug("[新版初始化] 用户 {} 已设置为在线状态", userId);
            
            // 使用GroupInitializationService将用户加入到群组缓存
            int groupCount = groupInitializationService.joinUserToAllGroupCache(userId, userGroups, session);
            
            log.info("[新版初始化] 用户 {} 初始化完成，共加入 {} 个群组", userId, groupCount);
            return true;
            
        } catch (Exception e) {
            log.error("[新版初始化] 用户 {} 新版群组初始化失败", userIdStr, e);
            return false;
        }
    }
    
    /**
     * 旧版群组初始化实现（向后兼容）
     */
    @Deprecated
    private void initializeUserGroupsCacheLegacy(String userIdStr, WebSocketSession session) {
        try {
            // 检查是否为监控端连接
            if ("monitor".equals(userIdStr)) {
                log.info("[WebSocket 主处理器] 监控端连接，设置会话用户名: Session ID = {}", session.getId());
                groupManager.setUserSession(session, "monitor", null);
                return;
            }

            Long userId = Long.parseLong(userIdStr);
            log.info("[WebSocket 主处理器] 开始初始化用户 {} 的群组缓存, Session ID = {}", userId, session.getId());

            // 1. 调用服务层获取用户所有相关的课程和分组信息
            List<StudentCourseInfoDTO> allCourseInfos = stSysUserService.getStudentCourseInfo(userId);
            if (allCourseInfos == null || allCourseInfos.isEmpty()) {
                log.warn("[WebSocket 主处理器] 用户 {} 没有任何课程或分组信息，缓存初始化中止。", userId);
                return;
            }

            // 2. 将用户设置为在线状态
            redisUserConnectionManager.userOnline(userId);
            log.debug("[WebSocket 主处理器] 用户 {} 已设置为在线状态", userId);

            // 3. 遍历信息列表，将用户加入到对应的群组缓存
            int groupCount = 0;
            for (StudentCourseInfoDTO info : allCourseInfos) {
                try {
                    String groupId = info.getGroupId();
                    if (!StringUtils.hasText(groupId) || "null".equalsIgnoreCase(groupId)) {
                        continue; // 跳过无效的 groupId
                    }

                    if (info.getIsCourse() != null && info.getIsCourse()) {
                        // 是课程频道
                        redisUserConnectionManager.userJoinGroup(
                            groupId, // groupId 在服务层已设置为 courseId
                            userId,
                            info.getUserName(),
                            info.getNickName(),
                            StringUtils.hasText(info.getAvatar()) ? info.getAvatar() : "",
                            session.getId()
                        );
                        log.info("[WebSocket 主处理器] 用户 {} 已成功加入课程频道 {} ({})",
                                userId, groupId, info.getGroupName());
                    } else {
                        // 是课程内的分组
                        String displayAvatar = StringUtils.hasText(info.getPuppetIcon())
                                ? info.getPuppetIcon()
                                : (StringUtils.hasText(info.getAvatar()) ? info.getAvatar() : "");

                        redisUserConnectionManager.userJoinGroup(
                            groupId,
                            userId,
                            info.getUserName(),
                            info.getPuppetName() != null ? info.getPuppetName() : info.getNickName(),
                            displayAvatar,
                            session.getId()
                        );
                        String avatarSource = StringUtils.hasText(info.getPuppetIcon()) ? "puppetIcon" :
                                (StringUtils.hasText(info.getAvatar()) ? "avatar" : "default");
                        log.info("[WebSocket 主处理器] 用户 {} 已成功加入课程分组 {} ({}), 头像来源: {}",
                                userId, groupId, info.getGroupName(), avatarSource);
                    }
                    groupCount++;

                } catch (Exception e) {
                    log.error("[WebSocket 主处理器] 用户 {} 加入群组 {} 失败", userId, info.getGroupId(), e);
                }
            }

            log.info("[WebSocket 主处理器] 用户 {} 群组缓存初始化完成，成功加入 {} 个群组/频道", userId, groupCount);

        } catch (NumberFormatException e) {
            log.error("[WebSocket 主处理器] 用户ID格式错误: {}, Session: {}", userIdStr, session.getId(), e);
        } catch (Exception e) {
            log.error("[WebSocket 主处理器] 初始化用户 {} 群组缓存时发生异常, Session: {}", userIdStr, session.getId(), e);
        }
    }

    /**
     * 收到文本消息时调用。
     * 解析消息，根据消息类型分发给相应的处理器。
     */
    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String payload = message.getPayload();
        log.debug("[WebSocket 主处理器] 收到来自 Session {} 的消息: {}", session.getId(), payload);

        try {
            // 1. 解析消息负载为 ChatMessage 对象
            ChatMessage chatMessage = objectMapper.readValue(payload, ChatMessage.class);

            // 2. 校验消息类型是否有效
            MessageType messageType = chatMessage.getType();
            if (messageType == null) {
                log.warn("[WebSocket 主处理器] 收到缺少类型的消息: Session ID = {}, Payload = {}", session.getId(), payload);
                sendErrorMessage(session, "Message type is missing");
                return;
            }

            // 3. 根据消息类型查找对应的处理器
            MessageHandler handler = messageHandlers.get(messageType);

            // 4. 如果找到处理器，则调用其 handle 方法
            if (handler != null) {
                try {
                    // 如果会话还没有初始化用户缓存，尝试从消息中获取用户信息进行初始化
                    if (!isSessionInitialized(session) && chatMessage.getSender() != null) {
                        try {
                            initializeUserGroupsCache(chatMessage.getSender(), session);
                            markSessionAsInitialized(session);
                        } catch (Exception e) {
                            log.warn("[WebSocket 主处理器] 从消息中初始化用户群组缓存失败: Session {}, Sender: {}",
                                    session.getId(), chatMessage.getSender(), e);
                        }
                    }

                    // 对于CHAT消息，在处理前检查群组是否有在线用户，如果没有则尝试重新初始化
                    if (chatMessage.getType() == MessageType.CHAT && chatMessage.getGroupId() != null && chatMessage.getSender() != null) {
                        List<GroupUserInfo> groupUsers = redisUserConnectionManager.getGroupUsers(chatMessage.getGroupId());
                        if (groupUsers.isEmpty()) {
                            log.warn("[WebSocket 主处理器] 群组 '{}' 中没有在线用户，尝试重新初始化发送者 '{}' 的群组缓存",
                                    chatMessage.getGroupId(), chatMessage.getSender());
                            try {
                                // 强制重新初始化发送者的群组缓存
                                initializeUserGroupsCache(chatMessage.getSender(), session);
                                markSessionAsInitialized(session);

                                // 重新检查群组用户
                                groupUsers = redisUserConnectionManager.getGroupUsers(chatMessage.getGroupId());
                                if (!groupUsers.isEmpty()) {
                                    log.info("[WebSocket 主处理器] 重新初始化成功，群组 '{}' 现在有 {} 个在线用户",
                                            chatMessage.getGroupId(), groupUsers.size());
                                } else {
                                    log.warn("[WebSocket 主处理器] 重新初始化后群组 '{}' 仍然没有在线用户", chatMessage.getGroupId());
                                }
                            } catch (Exception e) {
                                log.error("[WebSocket 主处理器] 重新初始化发送者 '{}' 的群组缓存失败", chatMessage.getSender(), e);
                            }
                        }
                    }

                    // 直接调用处理器处理消息，不再需要特殊处理JOIN消息的Redis缓存
                    handler.handle(session, chatMessage);
                    
                    // 5. 检查是否需要存储消息
                    if (handler.shouldStore(chatMessage)) {
                        try {
                            chatMessageStore.storeMessage(chatMessage);
                            log.debug("[WebSocket 主处理器] 消息已存储: Type={}, Sender={}, GroupId={}",
                                     chatMessage.getType(), chatMessage.getSender(), chatMessage.getGroupId());
                        } catch (Exception e) {
                            // 存储失败不应影响消息处理流程
                            log.error("[WebSocket 主处理器] 存储消息时出错: {}", chatMessage, e);
                        }
                    }
                } catch (Exception e) {
                    // 处理特定处理器可能抛出的异常
                    log.error("[WebSocket 主处理器] 处理消息时出错 (类型: {}, Session: {}): {}",
                              messageType, session.getId(), e.getMessage(), e);
                    sendErrorMessage(session, "Error processing your request: " + e.getMessage());
                }
            } else {
                // 5. 如果没有找到对应的处理器
                log.warn("[WebSocket 主处理器] 收到未知或未处理的消息类型: {} from Session {}",
                         messageType, session.getId());
                sendErrorMessage(session, "Unknown or unsupported message type: " + messageType);
            }

        } catch (JsonProcessingException e) {
            // JSON 解析失败
            log.error("[WebSocket 主处理器] 反序列化消息时出错: Session {} - {}: {}", session.getId(), payload, e.getMessage());
            sendErrorMessage(session, "Invalid message format");
        } catch (Exception e) {
            // 其他意外错误
            log.error("[WebSocket 主处理器] 处理消息时发生意外错误: Session {} - {}: {}", session.getId(), payload, e.getMessage(), e);
            sendErrorMessage(session, "An unexpected error occurred");
        }
    }

    /**
     * WebSocket 连接关闭后调用。
     * 从管理器中移除会话，并通知相关群组（如果用户已设置会话信息）。
     */
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        log.info("[WebSocket 主处理器] 连接关闭: Session ID = {}, Status = {}", session.getId(), status);

        // 获取用户信息（在会话关闭前获取）
        String username = groupManager.getUsername(session);
        String lastViewedGroupId = groupManager.getGroupId(session);

        // 处理 Redis 缓存清理
        if (username != null) {
            try {
                // 尝试解析用户ID
                Long userId = parseUserId(username);
                
                if (userId != null) {
                    // 从全局在线用户列表移除
                    redisUserConnectionManager.userOffline(userId);
                    
                    // 清理用户在所有群组中的状态
                    redisUserConnectionManager.clearUserFromAllGroups(userId);
                    
                    log.debug("[WebSocket 主处理器] 用户 {} (ID: {}) 的Redis缓存已全部清理", 
                             username, userId);
                } else {
                    // 如果无法解析为用户ID，仍然尝试从当前群组用户列表移除（使用0作为ID）
                    if (lastViewedGroupId != null) {
                        redisUserConnectionManager.userLeaveGroup(lastViewedGroupId, 0L);
                        log.debug("[WebSocket 主处理器] 用户 {} 离开群组 {} 的 Redis 缓存已清理（使用默认ID）", 
                                 username, lastViewedGroupId);
                    }
                }
            } catch (Exception e) {
                log.error("[WebSocket 主处理器] 清理断开连接用户的 Redis 缓存时出错: Session {}, Username: {}, GroupId: {}", 
                         session.getId(), username, lastViewedGroupId, e);
            }
        }

        // 从 GroupManager 移除会话（这会自动清理所有相关映射）
        boolean removedFromManager = groupManager.removeSession(session);

        // 如果用户有最后查看的群组，则广播断开连接消息
        if (username != null && lastViewedGroupId != null) {
            log.info("[WebSocket 主处理器] 用户 '{}' (Session {}) 断开连接，广播断开消息到群组 '{}'。", 
                    username, session.getId(), lastViewedGroupId);

            // 创建并广播 LEAVE 消息通知其他成员
            ChatMessage broadcastLeaveMsg = new ChatMessage();
            broadcastLeaveMsg.setType(MessageType.LEAVE);
            broadcastLeaveMsg.setSender(username);
            broadcastLeaveMsg.setGroupId(lastViewedGroupId);
            broadcastLeaveMsg.setContent(username + " 断开了连接");
            broadcastLeaveMsg.setTimestamp(java.time.Instant.now());

            groupManager.broadcastMessageToGroup(lastViewedGroupId, broadcastLeaveMsg);
        } else {
             // 如果用户未设置会话信息就断开，仅记录日志
            if (!removedFromManager) {
                log.warn("[WebSocket 主处理器] Session {} 关闭，但未在 GroupManager 中找到或已移除。", session.getId());
            } else {
                log.debug("[WebSocket 主处理器] 用户会话 {} 已正常关闭并清理。", session.getId());
            }
        }
    }

    /**
     * 尝试将 sender 解析为用户ID
     */
    private Long parseUserId(String sender) {
        if (sender == null || sender.trim().isEmpty()) {
            return null;
        }
        
        try {
            return Long.parseLong(sender.trim());
        } catch (NumberFormatException e) {
            // sender 不是数字，可能是用户名
            log.debug("[WebSocket 主处理器] Sender '{}' 不是有效的用户ID，将作为用户名处理", sender);
            return null;
        }
    }

    /**
     * 传输发生错误时调用。
     */
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        log.error("[WebSocket 主处理器] 传输错误: Session {}: {}",
                  Optional.ofNullable(session).map(WebSocketSession::getId).orElse("N/A"),
                  exception.getMessage());

        // 记录更详细的堆栈信息，方便排查
        // log.error("Transport error details:", exception);

        // 尝试安全地关闭会话
        // 注意：afterConnectionClosed 会被调用，处理后续的清理逻辑
        if (session != null && session.isOpen()) {
            try {
                session.close(CloseStatus.PROTOCOL_ERROR.withReason("Transport Error: " + exception.getMessage()));
            } catch (IOException e) {
                log.error("[WebSocket 主处理器] 关闭错误会话 {} 时发生 IO 异常: {}", session.getId(), e.getMessage());
            }
        }
        // 不需要在这里手动调用 groupManager.removeSession 或 removeSessionFromGroup
        // afterConnectionClosed 会处理这些
    }

    /**
     * 向客户端发送错误消息的辅助方法。
     *
     * @param session 目标会话
     * @param content 错误内容
     */
    private void sendErrorMessage(WebSocketSession session, String content) {
        try {
            // 创建一个标准的错误消息结构
            Map<String, String> errorPayload = Map.of(
                    "type", "ERROR",
                    "content", content
            );
            String messageJson = objectMapper.writeValueAsString(errorPayload);
            groupManager.sendMessageToSession(session, new TextMessage(messageJson)); // 使用 GroupManager 发送
        } catch (JsonProcessingException e) {
            log.error("[WebSocket 主处理器] 序列化错误消息时出错: {}", content, e);
        } catch (Exception e) {
            // 捕获发送过程中可能出现的其他异常 (例如 sendMessageToSession 可能抛出的)
             log.error("[WebSocket 主处理器] 发送错误消息到 Session {} 时失败: {}", session.getId(), e.getMessage());
        }
    }

    /**
     * 检查会话是否已经初始化用户缓存
     */
    private boolean isSessionInitialized(WebSocketSession session) {
        return session.getAttributes().containsKey("user_cache_initialized");
    }

    /**
     * 标记会话为已初始化
     */
    private void markSessionAsInitialized(WebSocketSession session) {
        session.getAttributes().put("user_cache_initialized", true);
    }

    // 不再需要 getLocalSessions 方法，由 WebSocketGroupManager 管理
    // 不再需要 handleJoin, handleLeave, handleChat 方法，由 MessageHandler 实现
    // 不再需要 broadcastMessageToGroup, removeSessionFromGroup 方法，由 WebSocketGroupManager 实现
} 