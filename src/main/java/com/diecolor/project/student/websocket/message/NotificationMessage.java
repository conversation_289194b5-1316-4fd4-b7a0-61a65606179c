package com.diecolor.project.student.websocket.message;

import com.diecolor.project.student.websocket.message.targets.*;
import lombok.Data;
import lombok.NonNull;

import java.time.Instant;
import java.util.Map;
import java.util.UUID;

/**
 * 通知消息实体类
 * 专门用于系统通知消息
 */
@Data
public class NotificationMessage implements BaseMessage {
    
    /**
     * 消息ID
     */
    private String id;
    
    /**
     * 发送者ID（通常为系统）
     */
    private String senderId;
    
    /**
     * 发送者名称
     */
    private String senderName;
    
    /**
     * 通知标题
     */
    private String title;
    
    /**
     * 通知内容
     */
    private String content;
    
    /**
     * 通知类型（如：系统公告、课程通知、作业提醒等）
     */
    private NotificationType notificationType;

    /**
     * 通知级别（如：info、warning、error、success）
     */
    private NotificationLevel level;
    
    /**
     * 消息目标
     */
    private MessageTarget messageTarget;
    
    /**
     * 时间戳
     */
    private Instant timestamp;
    
    /**
     * 扩展数据（可选）
     */
    private Map<String, Object> extraData;
    
    /**
     * 是否需要确认（用户是否需要点击确认）
     */
    private Boolean requireConfirmation;
    
    /**
     * 过期时间（可选，用于临时通知）
     */
    private Instant expireTime;
    
    /**
     * 构造函数
     */
    public NotificationMessage() {
        this.id = UUID.randomUUID().toString();
        this.timestamp = Instant.now();
        this.senderId = "system";
        this.senderName = "系统";
        this.level = NotificationLevel.INFO;
        this.requireConfirmation = false;
    }
    
    /**
     * 构造函数
     */
    public NotificationMessage(@NonNull String title, @NonNull String content, @NonNull MessageTarget messageTarget) {
        this();
        this.title = title;
        this.content = content;
        this.messageTarget = messageTarget;
    }
    
    /**
     * 构造函数（带通知类型）
     */
    public NotificationMessage(@NonNull String title, @NonNull String content,
                             @NonNull MessageTarget messageTarget, NotificationType notificationType) {
        this(title, content, messageTarget);
        this.notificationType = notificationType;
    }
    
    // BaseMessage 接口实现
    @Override
    public String getId() {
        return id;
    }
    
    @Override
    public MessageCategory getCategory() {
        return MessageCategory.SYSTEM_NOTIFY;
    }
    
    @Override
    public String getSenderId() {
        return senderId;
    }
    
    @Override
    public String getContent() {
        return content;
    }
    
    @Override
    public Instant getTimestamp() {
        return timestamp;
    }
    
    // 工厂方法
    
    /**
     * 创建全局广播通知
     */
    public static NotificationMessage createGlobalBroadcast(String title, String content) {
        return new NotificationMessage(title, content, new SystemBroadcastTarget());
    }
    
    /**
     * 创建班级通知
     */
    public static NotificationMessage createClassNotification(String title, String content, 
                                                            String classCode, String className) {
        return new NotificationMessage(title, content, new ClassNotificationTarget(classCode, className));
    }
    
    /**
     * 创建课程通知
     */
    public static NotificationMessage createCourseNotification(String title, String content, 
                                                             String courseId, String courseName) {
        return new NotificationMessage(title, content, new CourseNotificationTarget(courseId, courseName));
    }
    
    /**
     * 创建课程分组通知
     */
    public static NotificationMessage createCourseGroupNotification(String title, String content, 
                                                                  String groupId, String groupName, String courseId) {
        return new NotificationMessage(title, content, new CourseGroupNotificationTarget(groupId, groupName, courseId));
    }
    
    /**
     * 创建个人通知
     */
    public static NotificationMessage createPrivateNotification(String title, String content, 
                                                              String recipientId, String recipientName) {
        return new NotificationMessage(title, content, new PrivateMessageTarget(recipientId, recipientName));
    }
    
    /**
     * 设置通知级别
     */
    public NotificationMessage withLevel(NotificationLevel level) {
        this.level = level;
        return this;
    }

    /**
     * 设置通知级别（字符串版本，兼容性）
     */
    public NotificationMessage withLevel(String level) {
        this.level = NotificationLevel.fromCode(level);
        return this;
    }

    /**
     * 设置通知类型
     */
    public NotificationMessage withType(NotificationType notificationType) {
        this.notificationType = notificationType;
        return this;
    }

    /**
     * 设置通知类型（字符串版本，兼容性）
     */
    public NotificationMessage withType(String notificationType) {
        this.notificationType = NotificationType.fromCode(notificationType);
        return this;
    }
    
    /**
     * 设置是否需要确认
     */
    public NotificationMessage withConfirmation(boolean requireConfirmation) {
        this.requireConfirmation = requireConfirmation;
        return this;
    }
    
    /**
     * 设置过期时间
     */
    public NotificationMessage withExpireTime(Instant expireTime) {
        this.expireTime = expireTime;
        return this;
    }
    
    /**
     * 设置扩展数据
     */
    public NotificationMessage withExtraData(Map<String, Object> extraData) {
        this.extraData = extraData;
        return this;
    }
    
    /**
     * 判断通知是否已过期
     */
    public boolean isExpired() {
        return expireTime != null && Instant.now().isAfter(expireTime);
    }
    
    /**
     * 获取目标类型
     */
    public TargetType getTargetType() {
        return messageTarget != null ? messageTarget.getType() : null;
    }
    
    /**
     * 获取目标ID
     */
    public String getTargetId() {
        return messageTarget != null ? messageTarget.getTargetId() : null;
    }
    
    /**
     * 获取目标显示名称
     */
    public String getTargetDisplayName() {
        return messageTarget != null ? messageTarget.getDisplayName() : null;
    }
}
