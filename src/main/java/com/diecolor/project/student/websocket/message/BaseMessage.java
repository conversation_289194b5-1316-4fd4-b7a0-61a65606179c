package com.diecolor.project.student.websocket.message;

import java.time.Instant;

/**
 * 基础消息接口
 */
public interface BaseMessage {
    
    /**
     * 获取消息ID
     * @return 消息ID
     */
    String getId();
    
    /**
     * 获取消息类别
     * @return 消息类别
     */
    MessageCategory getCategory();
    
    /**
     * 获取发送者ID
     * @return 发送者ID
     */
    String getSenderId();
    
    /**
     * 获取消息内容
     * @return 消息内容
     */
    String getContent();
    
    /**
     * 获取消息时间戳
     * @return 消息时间戳
     */
    Instant getTimestamp();
}