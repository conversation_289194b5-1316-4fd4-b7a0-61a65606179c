package com.diecolor.project.student.websocket.message;

/**
 * 消息目标接口 - 轻量级设计
 * 支持群聊、私聊、系统通知等不同消息目标
 */
public interface MessageTarget {
    
    /**
     * 获取目标类型
     * @return 目标类型
     */
    TargetType getType();
    
    /**
     * 获取目标标识符
     * @return 目标ID
     */
    String getTargetId();
    
    /**
     * 获取目标显示名称（可选）
     * @return 显示名称，可以为null
     */
    default String getDisplayName() {
        return null;
    }
}