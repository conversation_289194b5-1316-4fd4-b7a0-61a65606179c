package com.diecolor.project.student.websocket.handler;

import com.diecolor.project.student.websocket.ChatMessage;
import com.diecolor.project.student.websocket.MessageType;
import com.diecolor.project.student.websocket.WebSocketMessagingUtils;
import com.diecolor.project.student.websocket.support.WebSocketGroupManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;

/**
 * 处理 JOIN 类型消息的处理器（简化版）
 * 
 * 重构说明：
 * - JOIN消息现在主要用于设置用户会话信息和业务通知
 * - 不再维护实际的群组会话映射
 * - 用户可以向任何有权限的群组发送消息，无需显式加入
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JoinMessageHandler implements MessageHandler {

    private final WebSocketGroupManager groupManager;

    @Override
    public void handle(WebSocketSession session, ChatMessage joinMessage) throws IOException {
        String username = joinMessage.getSender();
        String groupId = joinMessage.getGroupId();

        // 基础校验
        if (username == null || groupId == null) {
            log.warn("[JoinHandler] 无效的 JOIN 消息，缺少 username 或 groupId: Session ID = {}", session.getId());
            groupManager.sendMessageToSession(session, new TextMessage("{\"type\":\"ERROR\", \"content\":\"Username and GroupId are required for JOIN\"}"));
            return;
        }

        log.info("[JoinHandler] 用户 '{}' 请求查看群组 '{}'. Session ID: {}", username, groupId, session.getId());

        // 设置用户会话信息（主要用于兼容和当前群组跟踪）
        groupManager.setUserSession(session, username, groupId);

        // 准备并广播 JOIN 消息给所有用户（用于业务通知）
        ChatMessage broadcastJoinMsg = new ChatMessage();
        broadcastJoinMsg.setType(MessageType.JOIN);
        broadcastJoinMsg.setSender(username);
        broadcastJoinMsg.setGroupId(groupId);
        broadcastJoinMsg.setContent(username + " 加入了群聊");
        broadcastJoinMsg.setTimestamp(java.time.Instant.now());

        // 通过 GroupManager 广播消息
        groupManager.broadcastMessageToGroup(groupId, broadcastJoinMsg);

        log.debug("[JoinHandler] 用户 '{}' JOIN消息处理完成，当前查看群组: {}", username, groupId);
    }
} 