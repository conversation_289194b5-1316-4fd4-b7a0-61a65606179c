package com.diecolor.project.student.websocket.message.targets;

import com.diecolor.project.student.websocket.message.MessageTarget;
import com.diecolor.project.student.websocket.message.TargetType;
import lombok.Data;

/**
 * 课程通知消息目标
 * 用于向特定课程的所有学生发送通知
 */
@Data
public class CourseNotificationTarget implements MessageTarget {
    
    private final String courseId;
    private final String courseName;
    
    public CourseNotificationTarget(String courseId, String courseName) {
        this.courseId = courseId;
        this.courseName = courseName;
    }
    
    public CourseNotificationTarget(String courseId) {
        this(courseId, null);
    }
    
    @Override
    public TargetType getType() {
        return TargetType.COURSE_NOTIFICATION;
    }
    
    @Override
    public String getTargetId() {
        return courseId;
    }
    
    @Override
    public String getDisplayName() {
        return courseName != null ? courseName : "课程" + courseId;
    }
}
