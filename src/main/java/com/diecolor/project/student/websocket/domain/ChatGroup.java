package com.diecolor.project.student.websocket.domain;

import java.util.Set;

/**
 * 聊天群组接口 - 方案1轻量级设计
 * 专门用于群聊场景的抽象，不包含私聊和系统通知
 */
public interface ChatGroup {
    
    /**
     * 获取群组ID
     * @return 群组ID
     */
    String getGroupId();
    
    /**
     * 获取群组类型
     * @return 群组类型
     */
    GroupType getGroupType();
    
    /**
     * 获取群组显示名称
     * @return 群组名称
     */
    String getDisplayName();
    
    /**
     * 判断用户是否可以加入该群组
     * @param userId 用户ID
     * @return 是否可以加入
     */
    boolean canUserJoin(String userId);
    
    /**
     * 获取群组成员ID列表（可选实现）
     * 用于权限验证和成员管理
     * @return 成员ID集合，如果不支持则返回空集合
     */
    default Set<String> getMembers() {
        return Set.of();
    }
    
    /**
     * 获取群组的业务上下文信息（可选）
     * 例如：courseId, classId等
     * @return 业务上下文，可以为null
     */
    default Object getBusinessContext() {
        return null;
    }
}