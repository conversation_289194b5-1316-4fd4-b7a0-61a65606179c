package com.diecolor.project.student.websocket.initialization.impl;

import com.diecolor.project.scenario.domain.TeClassInfo;
import com.diecolor.project.scenario.mapper.TeClassInfoMapper;
import com.diecolor.project.student.dto.GroupUserDTO;
import com.diecolor.project.student.mapper.StSysUserMapper;
import com.diecolor.project.student.service.IStSysUserService;
import com.diecolor.project.student.websocket.config.ChatRoomProperties;
import com.diecolor.project.student.websocket.domain.ChatGroup;
import com.diecolor.project.student.websocket.domain.ClassGroup;
import com.diecolor.project.student.websocket.domain.GroupType;
import com.diecolor.project.student.websocket.initialization.GroupInitializer;
import com.diecolor.project.student.websocket.support.RedisUserConnectionManager;
import com.diecolor.project.system.domain.SysUser;
import com.diecolor.project.system.mapper.SysUserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketSession;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 班级群组初始化器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ClassGroupInitializer implements GroupInitializer {
    
    private final ChatRoomProperties chatGroupProperties;
    private final RedisUserConnectionManager redisUserConnectionManager;
    private final TeClassInfoMapper teClassInfoMapper;
    private final SysUserMapper sysUserMapper;
    private final StSysUserMapper stSysUserMapper;

    @Override
    public GroupType getSupportedType() {
        return GroupType.CLASS;
    }
    
    @Override
    public List<ChatGroup> initializeUserGroups(String userId) {
        log.debug("初始化用户班级群组, userId: {}", userId);

        SysUser sysUser = sysUserMapper.selectUserById(Long.parseLong(userId));
        String username = sysUser.getUserName();

        TeClassInfo teClassInfo = teClassInfoMapper.selectClassInfoByStudentCode(username);

        // 班级信息
        ClassGroup classGroup = new ClassGroup(teClassInfo.getClassCode(), teClassInfo.getClassName());
        // 班级成员
        List<GroupUserDTO> members = stSysUserMapper.selectClassMembersByUserName(username);
        classGroup.setMemberIds(members.stream().map(x -> x.getUserId().toString()).collect(Collectors.toSet()));

        List<ChatGroup> list = new ArrayList<>();
        list.add(classGroup);
        return list;
    }
    
    @Override
    public boolean isEnabled() {
        return chatGroupProperties.getClassRoom().isEnabled();
    }
    
    @Override
    public int getPriority() {
        return 10; // 班级群优先级较高
    }
    
    @Override
    public void joinUserToGroupCache(Long userId, ChatGroup group, WebSocketSession session, com.diecolor.project.student.websocket.initialization.UserInfoCache userInfoCache) {
        if (!(group instanceof ClassGroup)) {
            log.warn("ClassGroupInitializer 收到非班级群组: {}", group.getClass().getSimpleName());
            return;
        }
        
        ClassGroup classGroup = (ClassGroup) group;
        
        // 使用缓存获取用户信息，避免数据库查询
        String userName = userInfoCache.getUserNickName(userId);
        String userAvatar = userInfoCache.getUserAvatar(userId);
        
        redisUserConnectionManager.userJoinGroup(
            classGroup.getGroupId(),
            userId,
            userName,
            classGroup.getDisplayName(),
            userAvatar,
            session.getId()
        );
        
        log.info("[班级群组缓存] 用户 {} 已成功加入班级群 {} ({})",
                userId, classGroup.getGroupId(), classGroup.getDisplayName());
    }
}