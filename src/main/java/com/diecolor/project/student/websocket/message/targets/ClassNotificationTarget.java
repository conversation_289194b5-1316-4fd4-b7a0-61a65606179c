package com.diecolor.project.student.websocket.message.targets;

import com.diecolor.project.student.websocket.message.MessageTarget;
import com.diecolor.project.student.websocket.message.TargetType;
import lombok.Data;

/**
 * 班级通知消息目标
 * 用于向特定班级发送通知
 */
@Data
public class ClassNotificationTarget implements MessageTarget {
    
    private final String classCode;
    private final String className;
    
    public ClassNotificationTarget(String classCode, String className) {
        this.classCode = classCode;
        this.className = className;
    }
    
    public ClassNotificationTarget(String classCode) {
        this(classCode, null);
    }
    
    @Override
    public TargetType getType() {
        return TargetType.CLASS_NOTIFICATION;
    }
    
    @Override
    public String getTargetId() {
        return classCode;
    }
    
    @Override
    public String getDisplayName() {
        return className != null ? className : "班级" + classCode;
    }
}
