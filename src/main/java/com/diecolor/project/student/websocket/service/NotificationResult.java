package com.diecolor.project.student.websocket.service;

import lombok.Data;

/**
 * 通知发送结果
 */
@Data
public class NotificationResult {
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 结果消息
     */
    private String message;
    
    /**
     * 发送的用户数量
     */
    private int sentCount;
    
    /**
     * 总用户数量
     */
    private int totalCount;
    
    /**
     * 错误代码（可选）
     */
    private String errorCode;
    
    private NotificationResult(boolean success, String message) {
        this.success = success;
        this.message = message;
    }
    
    private NotificationResult(boolean success, String message, int sentCount, int totalCount) {
        this.success = success;
        this.message = message;
        this.sentCount = sentCount;
        this.totalCount = totalCount;
    }
    
    /**
     * 创建成功结果
     */
    public static NotificationResult success(String message) {
        return new NotificationResult(true, message);
    }
    
    /**
     * 创建成功结果（带统计信息）
     */
    public static NotificationResult success(String message, int sentCount, int totalCount) {
        return new NotificationResult(true, message, sentCount, totalCount);
    }
    
    /**
     * 创建失败结果
     */
    public static NotificationResult failure(String message) {
        return new NotificationResult(false, message);
    }
    
    /**
     * 创建失败结果（带错误代码）
     */
    public static NotificationResult failure(String message, String errorCode) {
        NotificationResult result = new NotificationResult(false, message);
        result.errorCode = errorCode;
        return result;
    }
}
