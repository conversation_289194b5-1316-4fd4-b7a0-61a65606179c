package com.diecolor.project.student.websocket.message.targets;

import com.diecolor.project.student.websocket.message.MessageTarget;
import com.diecolor.project.student.websocket.message.TargetType;
import lombok.Data;

/**
 * 课程分组通知消息目标
 * 用于向特定课程分组发送通知
 */
@Data
public class CourseGroupNotificationTarget implements MessageTarget {
    
    private final String groupId;
    private final String groupName;
    private final String courseId;
    
    public CourseGroupNotificationTarget(String groupId, String groupName, String courseId) {
        this.groupId = groupId;
        this.groupName = groupName;
        this.courseId = courseId;
    }
    
    public CourseGroupNotificationTarget(String groupId, String courseId) {
        this(groupId, null, courseId);
    }
    
    @Override
    public TargetType getType() {
        return TargetType.COURSE_GROUP_NOTIFICATION;
    }
    
    @Override
    public String getTargetId() {
        return groupId;
    }
    
    @Override
    public String getDisplayName() {
        return groupName != null ? groupName : "分组" + groupId;
    }
    
    /**
     * 获取课程ID
     * @return 课程ID
     */
    public String getCourseId() {
        return courseId;
    }
}
