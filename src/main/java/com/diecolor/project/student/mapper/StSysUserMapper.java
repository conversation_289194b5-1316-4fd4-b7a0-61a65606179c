package com.diecolor.project.student.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.diecolor.project.student.domain.StSysUser;
import com.diecolor.project.student.domain.dto.StudentCourseInfoDTO;
import com.diecolor.project.student.dto.GroupUserDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 学生端系统用户 数据层
 *
 * <AUTHOR>
 */
@Mapper
public interface StSysUserMapper extends BaseMapper<StSysUser> {
    
    /**
     * 查询学生课程信息
     *
     * @param userName 用户名
     * @param userId 用户ID
     * @return 学生课程信息
     */
    List<StudentCourseInfoDTO> selectStudentCourseInfo(@Param("userName") String userName);

    /**
     * 根据课程分组ID查询分组内的用户信息（包含马甲信息）
     *
     * @param groupId 课程分组ID
     * @return 用户信息列表 (包含马甲)
     */
    List<GroupUserDTO> selectGroupUsersByGroupId(@Param("groupId") String groupId);

    /**
     * 查询当前用户所在班级的课程群信息
     *
     * @param userName 用户名
     * @param userId 用户ID
     * @return 课程群信息列表
     */
    List<StudentCourseInfoDTO> selectStudentCourseGroupInfo(@Param("userName") String userName, @Param("userId") Long userId);

    /**
     * 根据课程ID查询该课程下所有学生信息（用于课程群）
     * 查询通过DC_COURSE_STUDENT表关联的学生
     *
     * @param courseId 课程ID
     * @return 课程下的学生信息列表
     */
    List<GroupUserDTO> selectCourseUsersByCourseId(@Param("courseId") String courseId);

    /**
     * 根据课程ID查询该课程下通过班级代码关联的所有学生信息（用于课程群）
     * 查询通过ST_STUDENT_INFO.STUDENT_CLASSCODE = DC_COURSE.CLASS_CODE关联的学生
     *
     * @param courseId 课程ID
     * @return 课程下的学生信息列表
     */
    List<GroupUserDTO> selectCourseUsersByClassCode(@Param("courseId") String courseId);

    /**
     * 根据课程ID查询课程群的成员列表
     *
     * @param courseId 课程ID
     * @return 成员列表
     */
    List<GroupUserDTO> selectCourseMembersByCourseId(@Param("courseId") String courseId);

    /**
     * 根据分组ID查询分组的成员列表（包含马甲信息）
     *
     * @param groupId 分组ID
     * @return 成员列表
     */
    List<GroupUserDTO> selectGroupMembersByGroupId(@Param("groupId") String groupId);

    /**
     * 查询学生进行中的课程信息
     * 只返回状态为1（进行中）的课程
     *
     * @param userName 用户名
     * @return 学生进行中的课程信息
     */
    List<StudentCourseInfoDTO> selectStudentActiveCourseInfo(@Param("userName") String userName);

    /**
     * 根据课程ID查询学生的具体课程信息
     *
     * @param userName 用户名
     * @param courseId 课程ID
     * @return 学生的具体课程信息
     */
    StudentCourseInfoDTO selectStudentCourseInfo(@Param("userName") String userName, @Param("courseId") String courseId);

    /**
     * 查询学生所有的课程信息（不限制状态）
     * 
     * @param userName 用户名
     * @return 学生所有的课程信息
     */
    List<StudentCourseInfoDTO> selectStudentAllCourseInfo(@Param("userName") String userName);

    /**
     * 查询当前用户所在班级的所有成员
     * 
     * @param userName 用户名
     * @return 班级成员列表
     */
    List<GroupUserDTO> selectClassMembersByUserName(@Param("userName") String userName);
} 