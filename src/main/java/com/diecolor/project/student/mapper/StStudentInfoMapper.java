package com.diecolor.project.student.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.diecolor.project.student.domain.StStudentInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 学生信息Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface StStudentInfoMapper extends BaseMapper<StStudentInfo> {

    /**
     * 根据学生代码查询学生班级代码
     *
     * @param studentCode 学生代码
     * @return 学生班级代码
     */
    @Select("SELECT student_classcode FROM ST_STUDENT_INFO WHERE STUDENT_CODE = #{studentCode}")
    String selectClassCodeByStudentCode(@Param("studentCode") String studentCode);
}