package com.diecolor.project.student.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 学生信息实体类
 * 对应表：ST_STUDENT_INFO
 * 
 * <AUTHOR>
 */
@Data
@TableName("ST_STUDENT_INFO")
public class StStudentInfo implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 学生代码
     */
    @TableField("STUDENT_CODE")
    private String studentCode;

    /**
     * 学生班级代码
     */
    @TableField("STUDENT_CLASSCODE")
    private String studentClassCode;

    /**
     * 学生姓名
     */
    @TableField("STUDENT_NAME")
    private String studentName;
}