package com.diecolor.project.student.domain.dto;

import lombok.Data;

/**
 * 学生课程信息VO
 */
@Data
public class StudentCourseInfoDTO {
    
    /** 学号/用户名 */
    private String userName;
    
    /** 姓名 */
    private String nickName;
    
    /** 用户头像 */
    private String avatar;
    
    /** 课程分组ID */
    private String groupId;
    
    /** 课程分组名称 */
    private String groupName;
    
    /** 马甲名称 */
    private String puppetName;
    
    /** 马甲图标 */
    private String puppetIcon;
    
    /** 课程ID */
    private String courseId;
    
    /** 课程名称 */
    private String courseName;
    
    /** 课程介绍 */
    private String courseIntroduction;
    
    /** 场景ID */
    private String sceneId;
    
    /** 场景模板名称 */
    private String sceneName;
    
    /** 场景描述 */
    private String sceneIntroduction;

    /** 场景图片 */
    private String sceneImage;

    /** 是否为课程大群（用于区分课程大群和普通分组） */
    private Boolean isPublic = false;
    
    /** 是否为组长 */
    private Boolean isGroupLeader = false;

    /** 是否为课程（用于前端区分课程和分组） */
    private Boolean isCourse;
    
    /** 教师姓名 */
    private String teacherName;
    
    /** 教师头像 */
    private String teacherAvatar;
    
    /** 课程开始时间 */
    private java.util.Date startTime;
    
    /** 课程结束时间 */
    private java.util.Date endTime;
    
    /** 课程状态 (0: 未开始, 1: 进行中, 2: 已结束) */
    private String courseState;
    
    /** 当前阶段 */
    private String currentStage;
    
    /** 课程状态 (ongoing: 进行中, upcoming: 即将开始, ended: 已结束) */
    private String status;
} 