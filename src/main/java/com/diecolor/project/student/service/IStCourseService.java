package com.diecolor.project.student.service;

import com.diecolor.project.student.domain.dto.StudentCourseInfoDTO;

import java.util.List;

/**
 * 学生端课程Service接口
 * 
 * <AUTHOR>
 */
public interface IStCourseService {

    /**
     * 获取当前学生的进行中课程列表
     * 只返回状态为1（进行中）的课程
     *
     * @return 进行中的课程列表
     */
    List<StudentCourseInfoDTO> getActiveCourseList();

    /**
     * 根据课程ID获取课程详细信息
     *
     * @param courseId 课程ID
     * @return 课程详细信息
     */
    StudentCourseInfoDTO getCourseInfo(String courseId);

    /**
     * 获取当前学生的所有课程列表
     * 用于课程首页的"我的课程"列表显示
     *
     * @param currentCourseId 当前课程ID（可选，如果提供则会在结果中过滤掉）
     * @return 所有课程列表
     */
    List<StudentCourseInfoDTO> getMyCourseList(String currentCourseId);
}
