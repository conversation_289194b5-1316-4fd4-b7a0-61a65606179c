package com.diecolor.project.student.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.diecolor.project.scenario.domain.DcCourse;
import com.diecolor.project.scenario.mapper.DcCourseMapper;
import com.diecolor.project.student.domain.dto.StudentCourseInfoDTO;
import com.diecolor.project.student.mapper.StSysUserMapper;
import com.diecolor.project.student.service.IStCourseService;
import com.diecolor.project.student.util.StAuthUtil;
import com.diecolor.project.student.domain.StLoginUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 学生端课程Service实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StCourseServiceImpl implements IStCourseService {

    private final StSysUserMapper stSysUserMapper;
    private final DcCourseMapper dcCourseMapper;

    /**
     * 获取当前学生的进行中课程列表
     * 只返回状态为1（进行中）的课程
     *
     * @return 进行中的课程列表
     */
    @Override
    public List<StudentCourseInfoDTO> getActiveCourseList() {
        // 获取当前登录用户信息
        StLoginUser loginUser = StAuthUtil.getRequiredLoginUser();
        String username = loginUser.getUsername();

        try {
            // 查询该学生加入的所有课程列表
            List<StudentCourseInfoDTO> allCourseList = stSysUserMapper.selectStudentActiveCourseInfo(username);
            
            if (allCourseList != null && !allCourseList.isEmpty()) {
                // 过滤出课程（非分组），并设置用户信息
                List<StudentCourseInfoDTO> activeCourseList = allCourseList.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
                
                // 为每个课程设置用户信息
                for (StudentCourseInfoDTO course : activeCourseList) {
                    course.setUserName(username);
                    course.setNickName(loginUser.getNickname());
                    course.setAvatar(loginUser.getAvatar());
                    course.setIsCourse(true); // 标记为课程
                }
                return activeCourseList;
            } else {
                return new ArrayList<>();
            }
        } catch (Exception e) {
            log.error("[StCourseService] 查询用户 {} 的进行中课程列表失败", username, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据课程ID获取课程详细信息
     *
     * @param courseId 课程ID
     * @return 课程详细信息
     */
    @Override
    public StudentCourseInfoDTO getCourseInfo(String courseId) {
        // 获取当前登录用户信息
        StLoginUser loginUser = StAuthUtil.getRequiredLoginUser();
        String username = loginUser.getUsername();

        try {
            DcCourse dcCourse = dcCourseMapper.selectById(courseId);
            if (dcCourse != null) {
                StudentCourseInfoDTO courseInfo = new StudentCourseInfoDTO();
                courseInfo.setCourseId(courseId);
                courseInfo.setCourseName(dcCourse.getCourseName());
                courseInfo.setUserName(username);
                courseInfo.setNickName(loginUser.getNickname());
                courseInfo.setAvatar(loginUser.getAvatar());
                courseInfo.setIsCourse(true);
                courseInfo.setStartTime(dcCourse.getCourseBeginTime());
                courseInfo.setEndTime(dcCourse.getCourseEndTime());
                courseInfo.setStatus(dcCourse.getCourseState());
                return courseInfo;
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("[StCourseService] 查询用户 {} 的课程 {} 信息失败", username, courseId, e);
            return null;
        }
    }

    /**
     * 获取当前学生的所有课程列表
     * 用于课程首页的"我的课程"列表显示
     *
     * @param currentCourseId 当前课程ID（可选，如果提供则会在结果中过滤掉）
     * @return 所有课程列表
     */
    @Override
    public List<StudentCourseInfoDTO> getMyCourseList(String currentCourseId) {
        // 获取当前登录用户信息
        StLoginUser loginUser = StAuthUtil.getRequiredLoginUser();
        String username = loginUser.getUsername();

        try {
            // 使用新的查询方法，查询该学生的所有课程（不限制状态）
            List<StudentCourseInfoDTO> allCourseList = stSysUserMapper.selectStudentAllCourseInfo(username);
            
            if (allCourseList != null && !allCourseList.isEmpty()) {
                // 过滤掉当前课程（如果提供了currentCourseId）
                List<StudentCourseInfoDTO> filteredCourseList = allCourseList.stream()
                    .filter(Objects::nonNull)
                    .filter(course -> currentCourseId == null || !currentCourseId.equals(course.getCourseId()))
                    .collect(Collectors.toList());
                
                // 为每个课程设置用户信息和状态
                for (StudentCourseInfoDTO course : filteredCourseList) {
                    course.setUserName(username);
                    course.setNickName(loginUser.getNickname());
                    course.setAvatar(loginUser.getAvatar());
                    course.setIsCourse(true); // 标记为课程
                    
                    // 根据课程状态设置状态文本
                    String courseState = course.getCourseState();
                    if ("0".equals(courseState)) {
                        course.setStatus("upcoming");
                        course.setCurrentStage("未开始");
                    } else if ("1".equals(courseState)) {
                        course.setStatus("ongoing");
                        course.setCurrentStage("进行中");
                    } else if ("2".equals(courseState)) {
                        course.setStatus("ended");
                        course.setCurrentStage("已结束");
                    } else {
                        course.setStatus("upcoming");
                        course.setCurrentStage("未开始");
                    }
                    
                    // 设置教师信息（暂时使用默认值）
                    course.setTeacherName("李教授");
                }
                
                log.info("[StCourseService] 查询用户 {} 的所有课程列表成功，共 {} 门课程，过滤后 {} 门课程", 
                    username, allCourseList.size(), filteredCourseList.size());
                return filteredCourseList;
            } else {
                return new ArrayList<>();
            }
        } catch (Exception e) {
            log.error("[StCourseService] 查询用户 {} 的所有课程列表失败", username, e);
            return new ArrayList<>();
        }
    }
}
