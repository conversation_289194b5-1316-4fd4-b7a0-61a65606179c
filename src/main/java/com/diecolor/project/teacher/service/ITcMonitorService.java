package com.diecolor.project.teacher.service;

import com.diecolor.project.teacher.vo.TcCourseInfoVO;
import com.diecolor.project.teacher.vo.TcMonitorStatisticsVO;

/**
 * 教师端监控Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
public interface ITcMonitorService {

    /**
     * 验证课程码是否有效
     * 
     * @param courseCode 课程码
     * @return 是否有效
     */
    boolean validateCourseCode(String courseCode);

    /**
     * 获取课程信息
     * 
     * @param courseCode 课程码
     * @return 课程信息
     */
    TcCourseInfoVO getCourseInfo(String courseCode);

    /**
     * 获取简化的课程信息
     * 
     * @param courseCode 课程码
     * @return 简化的课程信息
     */
    TcCourseInfoVO getCourseSimpleInfo(String courseCode);

    /**
     * 获取监控统计信息
     * 
     * @param courseCode 课程码（可选）
     * @return 统计信息
     */
    TcMonitorStatisticsVO getMonitorStatistics(String courseCode);

    /**
     * 更新课程状态
     * 
     * @param courseCode 课程码
     * @param courseState 课程状态（0-未开始，1-进行中，2-已结束）
     * @return 是否更新成功
     */
    boolean updateCourseState(String courseCode, String courseState);
} 