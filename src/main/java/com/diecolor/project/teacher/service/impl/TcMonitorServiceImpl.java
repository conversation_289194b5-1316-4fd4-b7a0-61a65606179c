package com.diecolor.project.teacher.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.diecolor.project.scenario.domain.DcCourse;
import com.diecolor.project.scenario.mapper.DcCourseMapper;
import com.diecolor.project.student.websocket.support.RedisUserConnectionManager;
import com.diecolor.project.teacher.dto.TcCourseGroupDTO;
import com.diecolor.project.teacher.service.ITcCourseGroupService;
import com.diecolor.project.teacher.service.ITcMonitorService;
import com.diecolor.project.teacher.vo.TcCourseInfoVO;
import com.diecolor.project.teacher.vo.TcMonitorStatisticsVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 教师端监控Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TcMonitorServiceImpl implements ITcMonitorService {

    private final DcCourseMapper dcCourseMapper;
    private final ITcCourseGroupService tcCourseGroupService;
    private final RedisUserConnectionManager redisUserConnectionManager;

    /**
     * 验证课程码是否有效
     * 
     * @param courseCode 课程码
     * @return 是否有效
     */
    @Override
    public boolean validateCourseCode(String courseCode) {
        log.debug("验证课程码: {}", courseCode);
        
        if (!StringUtils.hasText(courseCode)) {
            return false;
        }
        
        try {
            // 查询课程是否存在且状态正常
            LambdaQueryWrapper<DcCourse> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DcCourse::getCourseCode, courseCode)
                       .eq(DcCourse::getIsDelete, 0); // 未删除状态
            
            DcCourse course = dcCourseMapper.selectOne(queryWrapper);
            boolean isValid = course != null;
            
            log.debug("课程码 {} 验证结果: {}", courseCode, isValid);
            return isValid;
        } catch (Exception e) {
            log.error("验证课程码异常: {}", courseCode, e);
            return false;
        }
    }

    /**
     * 获取课程信息
     * 
     * @param courseCode 课程码
     * @return 课程信息
     */
    @Override
    public TcCourseInfoVO getCourseInfo(String courseCode) {
        log.debug("获取课程信息: {}", courseCode);
        
        if (!StringUtils.hasText(courseCode)) {
            return null;
        }
        
        try {
            // 查询课程基本信息
            LambdaQueryWrapper<DcCourse> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DcCourse::getCourseCode, courseCode)
                       .eq(DcCourse::getIsDelete, 0);
            
            DcCourse course = dcCourseMapper.selectOne(queryWrapper);
            if (course == null) {
                return null;
            }
            
            // 获取课程群组信息统计
            List<TcCourseGroupDTO> groups = tcCourseGroupService.getCourseGroupsByCourseCode(courseCode);
            
            // 计算统计数据
            int groupCount = groups.size();
            int totalStudents = groups.stream()
                    .mapToInt(group -> group.getStudentCount() != null ? group.getStudentCount() : 0)
                    .sum();
            
            // 计算在线学生数（从Redis获取）
            int onlineStudents = 0;
            try {
                for (TcCourseGroupDTO group : groups) {
                    onlineStudents += redisUserConnectionManager.getGroupUserCount(group.getGroupId());
                }
            } catch (Exception e) {
                log.warn("获取在线学生数失败", e);
            }
            
            // 构建返回对象
            return TcCourseInfoVO.builder()
                    .courseId(course.getCourseId())
                    .courseCode(course.getCourseCode())
                    .courseName(course.getCourseName())
                    .sceneName(course.getSceneId()) // 使用sceneId作为sceneName
                    .status(course.getIsDelete()) // 使用isDelete作为状态
                    .createTime(course.getCreateTime())
                    .groupCount(groupCount)
                    .totalStudents(totalStudents)
                    .onlineStudents(onlineStudents)
                    .startTime(course.getCourseBeginTime())
                    .endTime(course.getCourseEndTime())
                    .build();
        } catch (Exception e) {
            log.error("获取课程信息异常: {}", courseCode, e);
            return null;
        }
    }

    /**
     * 获取监控统计信息
     * 
     * @param courseCode 课程码（可选）
     * @return 统计信息
     */
    @Override
    public TcMonitorStatisticsVO getMonitorStatistics(String courseCode) {
        log.debug("获取监控统计信息，课程码: {}", courseCode);
        
        try {
            List<TcCourseGroupDTO> groups;
            
            if (StringUtils.hasText(courseCode)) {
                // 获取指定课程的群组
                groups = tcCourseGroupService.getCourseGroupsByCourseCode(courseCode);
            } else {
                // 获取所有群组（这里可以根据当前用户的权限进行过滤）
                groups = tcCourseGroupService.getCourseGroupsByDeptId(null);
            }
            
            // 计算统计数据
            int totalGroups = groups.size();
            int totalStudents = groups.stream()
                    .mapToInt(group -> group.getStudentCount() != null ? group.getStudentCount() : 0)
                    .sum();
            
            // 计算在线学生数
            int onlineStudents = 0;
            int activeGroups = 0;
            
            try {
                for (TcCourseGroupDTO group : groups) {
                    int groupOnline = redisUserConnectionManager.getGroupUserCount(group.getGroupId());
                    onlineStudents += groupOnline;
                    if (groupOnline > 0) {
                        activeGroups++;
                    }
                }
            } catch (Exception e) {
                log.warn("获取在线统计数据失败", e);
            }
            
            // 格式化当前时间
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String lastUpdateTime = sdf.format(new Date());
            
            return TcMonitorStatisticsVO.builder()
                    .totalGroups(totalGroups)
                    .totalStudents(totalStudents)
                    .onlineStudents(onlineStudents)
                    .todayMessages(0L) // 今日消息数暂时设为0，后续可以实现
                    .activeGroups(activeGroups)
                    .lastUpdateTime(lastUpdateTime)
                    .build();
        } catch (Exception e) {
            log.error("获取监控统计信息异常，课程码: {}", courseCode, e);
            throw new RuntimeException("获取统计信息失败", e);
        }
    }

    /**
     * 获取简化的课程信息
     * 
     * @param courseCode 课程码
     * @return 简化的课程信息
     */
    @Override
    public TcCourseInfoVO getCourseSimpleInfo(String courseCode) {
        if (!StringUtils.hasText(courseCode)) {
            return null;
        }
        
        try {
            // 只查询课程基本信息，不查询群组和统计数据
            LambdaQueryWrapper<DcCourse> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DcCourse::getCourseCode, courseCode)
                       .eq(DcCourse::getIsDelete, 0);
            
            DcCourse course = dcCourseMapper.selectOne(queryWrapper);
            if (course == null) {
                return null;
            }
            
            // 构建简化的返回对象，只包含基本课程信息
            return TcCourseInfoVO.builder()
                    .courseId(course.getCourseId())
                    .courseCode(course.getCourseCode())
                    .courseName(course.getCourseName())
                    .sceneName(course.getSceneId()) // 使用sceneId作为sceneName
                    .status(course.getIsDelete()) // 使用isDelete作为状态
                    .createTime(course.getCreateTime())
                    .startTime(course.getCourseBeginTime())
                    .endTime(course.getCourseEndTime())
                    // 简化版本不包含以下字段，减少查询开销
                    .groupCount(null)
                    .totalStudents(null)
                    .onlineStudents(null)
                    .build();
        } catch (Exception e) {
            log.error("获取简化课程信息异常: {}", courseCode, e);
            return null;
        }
    }

    /**
     * 更新课程状态
     * 
     * @param courseCode 课程码
     * @param courseState 课程状态（0-未开始，1-进行中，2-已结束）
     * @return 是否更新成功
     */
    @Override
    public boolean updateCourseState(String courseCode, String courseState) {
        log.debug("更新课程状态: {} -> {}", courseCode, courseState);
        
        if (!StringUtils.hasText(courseCode) || !StringUtils.hasText(courseState)) {
            log.warn("课程码或状态参数为空");
            return false;
        }
        
        try {
            // 查询课程是否存在
            LambdaQueryWrapper<DcCourse> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(DcCourse::getCourseCode, courseCode)
                       .eq(DcCourse::getIsDelete, 0);
            
            DcCourse course = dcCourseMapper.selectOne(queryWrapper);
            if (course == null) {
                log.warn("课程不存在: {}", courseCode);
                return false;
            }
            
            // 更新课程状态
            course.setCourseState(courseState);
            course.setModifyTime(new Date());
            
            // 如果是开始上课（状态为1），更新开始时间
            if ("1".equals(courseState)) {
                course.setCourseBeginTime(new Date());
            }
            // 如果是结束课程（状态为2），更新结束时间
            else if ("2".equals(courseState)) {
                course.setCourseEndTime(new Date());
            }
            
            int result = dcCourseMapper.updateById(course);
            boolean success = result > 0;
            
            log.debug("课程状态更新结果: {} -> {}, 结果: {}", courseCode, courseState, success);
            return success;
        } catch (Exception e) {
            log.error("更新课程状态异常: {} -> {}", courseCode, courseState, e);
            return false;
        }
    }
} 