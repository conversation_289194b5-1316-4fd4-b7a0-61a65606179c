package com.diecolor.project.teacher.vo;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.io.Serializable;
import java.util.Date;

/**
 * 教师端课程信息VO
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TcCourseInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 课程ID
     */
    private String courseId;

    /**
     * 课程代码
     */
    private String courseCode;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 班级ID
     */
    private Long deptId;

    /**
     * 班级名称
     */
    private String deptName;

    /**
     * 课程状态（0=正常 1=停用）
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 群组数量
     */
    private Integer groupCount;

    /**
     * 学生总数
     */
    private Integer totalStudents;

    /**
     * 在线学生数
     */
    private Integer onlineStudents;

    private Date startTime;

    private Date endTime;
} 