package com.diecolor.project.teacher.controller;

import com.diecolor.framework.aspectj.lang.annotation.Anonymous;
import com.diecolor.framework.web.controller.BaseController;
import com.diecolor.framework.web.domain.AjaxResult;
import com.diecolor.project.teacher.service.ITcMonitorService;
import com.diecolor.project.teacher.vo.TcCourseInfoVO;
import com.diecolor.project.teacher.vo.TcMonitorStatisticsVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 教师端监控管理Controller
 * 
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/teacher/monitor")
public class TcMonitorController extends BaseController {

    private final ITcMonitorService tcMonitorService;

    /**
     * 验证课程码是否有效
     * 
     * @param courseCode 课程码
     * @return 验证结果
     */
    @Anonymous
    @GetMapping("/validate-course-code/{courseCode}")
    public AjaxResult validateCourseCode(
            @PathVariable("courseCode") 
            @NotBlank(message = "课程码不能为空") 
            @Pattern(regexp = "^[A-Za-z0-9_-]{3,50}$", message = "课程码格式不正确")
            String courseCode) {
        
        log.info("验证课程码: {}", courseCode);
        boolean isValid = tcMonitorService.validateCourseCode(courseCode);
        return AjaxResult.success(isValid ? "课程码验证成功" : "课程码验证失败", isValid);
    }

    /**
     * 获取简化的课程信息
     * 
     * @param courseCode 课程码
     * @return 简化的课程信息
     */
    @Anonymous
    @GetMapping("/course/{courseCode}/simple")
    public AjaxResult getCourseSimpleInfo(
            @PathVariable("courseCode") 
            @NotBlank(message = "课程码不能为空")
            String courseCode) {
        
        try {
            TcCourseInfoVO courseInfo = tcMonitorService.getCourseSimpleInfo(courseCode);
            
            if (courseInfo != null) {
                return AjaxResult.success(courseInfo);
            } else {
                return AjaxResult.error("课程信息不存在");
            }
        } catch (Exception e) {
            log.error("获取简化课程信息异常: {}", courseCode, e);
            return AjaxResult.error("获取简化课程信息失败");
        }
    }

    /**
     * 获取监控统计信息
     * 
     * @param courseCode 课程码（可选）
     * @return 统计信息
     */
    @GetMapping("/statistics")
    public AjaxResult getMonitorStatistics(
            @RequestParam(value = "courseCode", required = false) String courseCode) {
        
        log.info("获取监控统计信息，课程码: {}", courseCode);
        
        try {
            TcMonitorStatisticsVO statistics = tcMonitorService.getMonitorStatistics(courseCode);
            return AjaxResult.success(statistics);
        } catch (Exception e) {
            log.error("获取监控统计信息异常，课程码: {}", courseCode, e);
            return AjaxResult.error("获取统计信息失败");
        }
    }

    /**
     * 更新课程状态
     * 
     * @param courseCode 课程码
     * @param courseState 课程状态（0-未开始，1-进行中，2-已结束）
     * @return 更新结果
     */
    @Anonymous
    @PutMapping("/course/{courseCode}/state")
    public AjaxResult updateCourseState(
            @PathVariable("courseCode") 
            @NotBlank(message = "课程码不能为空")
            String courseCode,
            @RequestParam("courseState") 
            @NotBlank(message = "课程状态不能为空")
            String courseState) {
        
        log.info("更新课程状态: {} -> {}", courseCode, courseState);
        
        try {
            boolean success = tcMonitorService.updateCourseState(courseCode, courseState);
            
            if (success) {
                return AjaxResult.success("课程状态更新成功");
            } else {
                return AjaxResult.error("课程状态更新失败");
            }
        } catch (Exception e) {
            log.error("更新课程状态异常: {} -> {}", courseCode, courseState, e);
            return AjaxResult.error("更新课程状态失败");
        }
    }

    /**
     * 结束课堂
     * 
     * @param courseCode 课程码
     * @return 结束结果
     */
    @Anonymous
    @PutMapping("/course/{courseCode}/end")
    public AjaxResult endClass(
            @PathVariable("courseCode") 
            @NotBlank(message = "课程码不能为空")
            String courseCode) {
        
        log.info("结束课堂: {}", courseCode);
        
        try {
            boolean success = tcMonitorService.updateCourseState(courseCode, "2");
            
            if (success) {
                return AjaxResult.success("课堂已结束");
            } else {
                return AjaxResult.error("结束课堂失败");
            }
        } catch (Exception e) {
            log.error("结束课堂异常: {}", courseCode, e);
            return AjaxResult.error("结束课堂失败");
        }
    }

    /**
     * 获取课程信息
     *
     * @param courseCode 课程码
     * @return 课程信息
     */
    @Anonymous
    @GetMapping("/course/{courseCode}")
    public AjaxResult getCourseInfo(
            @PathVariable("courseCode")
            @NotBlank(message = "课程码不能为空")
            String courseCode) {

        log.info("获取课程信息: {}", courseCode);

        try {
            TcCourseInfoVO courseInfo = tcMonitorService.getCourseInfo(courseCode);

            if (courseInfo != null) {
                return AjaxResult.success(courseInfo);
            } else {
                return AjaxResult.error("课程信息不存在");
            }
        } catch (Exception e) {
            log.error("获取课程信息异常: {}", courseCode, e);
            return AjaxResult.error("获取课程信息失败");
        }
    }
} 