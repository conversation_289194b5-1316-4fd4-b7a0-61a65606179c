package com.diecolor.project.aiflow.service;

import com.diecolor.common.utils.DateUtils;
import com.diecolor.project.ai.bean.Survey;
import com.diecolor.project.ai.util.SurveyParser;
import com.diecolor.project.ai.util.SurveyToPaper;
import com.diecolor.project.aiflow.llm.QwenClientFlow;
import com.diecolor.project.scenario.domain.*;
import com.diecolor.project.scenario.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

@Service
public class AutoQuestionFlowService {

    @Autowired
    private QwenClientFlow client;


    @Autowired
    private IDcSceneTestpaperService dcSceneTestpaperService;
    @Autowired
    private IDcSceneQuestionService dcSceneQuestionService;
    @Autowired
    private IDcSceneQuestionItemService dcSceneQuestionItemService;

    @Autowired
    private IDcCourseTestpaperService dcCourseTestpaperService;
    @Autowired
    private IDcCourseQuestionService dcCourseQuestionService;
    @Autowired
    private IDcCourseQuestionItemService dcCourseQuestionItemService;



    public void generateSurvey(String topic, int questionCount,QwenClientFlow.ResponseCallback callback) throws IOException {
        // 构建提示词
        String systemPrompt = "你是一个专业的问卷生成助手，请根据用户需求生成结构完整的调查问卷。" +
                "请以JSON格式返回结果，确保包含title、description和questions字段,question的结构是 question,type,options。";

        String userPrompt = String.format("请生成一个关于'%s'的调查问卷，包含%d个问题。" +
                "问卷应包含多种问题类型（单选题、多选题、开放式问题）。", topic, questionCount);
        try {
                client.callQwenAPI(systemPrompt, userPrompt,callback);
            } catch (IOException e) {
                System.err.println("调用Qwen API时发生错误: " + e.getMessage());
            }
    }


    public void saveSurvey(String result,String sceneid,String userid) {
        Survey survey=null;
        if (result!=null)
        {
             survey= SurveyParser.parseSurveyWithMarkdown(result);
        }
        if (survey!=null){
            DcSceneTestpaper testpaper= SurveyToPaper.surveyToSceneTestpaper(survey);

            testpaper.setSceneId(sceneid);
            testpaper.setCreateUser(userid);
            testpaper.setCreateTime(DateUtils.getNowDate());
            boolean saved=dcSceneTestpaperService.save(testpaper);
            if (saved){
                List<DcSceneQuestion> questions=testpaper.getQuestions();
                for (DcSceneQuestion qt:questions
                ) {
                    qt.setPaperId(testpaper.getPaperId());
                    qt.setCreateTime(DateUtils.getNowDate());
                    qt.setCreateUser(userid);
                    boolean qtsaved=dcSceneQuestionService.save(qt);
                    if (qtsaved)
                    {
                        List<DcSceneQuestionItem> items=qt.getItems();
                        for (DcSceneQuestionItem item:items
                        )
                        {
                            item.setQuestionId(qt.getQuestionId());
                            item.setCreateTime(DateUtils.getNowDate());
                            item.setCreateUser(userid);
                            dcSceneQuestionItemService.save(item);
                        }
                    }


                }
            }
        }
        else {
            System.out.println("问卷调查 生产数量是零");
        }
    }



    public void saveCourseSurvey(String result,String courseid,String userid) {
        Survey survey=null;
        if (result!=null)
        {
            survey= SurveyParser.parseSurveyWithMarkdown(result);
        }
        if (survey!=null){
            DcCourseTestpaper testpaper= SurveyToPaper.surveyToCourseTestpaper(survey);

            testpaper.setCourseId(courseid);
            testpaper.setCreateUser(userid);
            testpaper.setCreateTime(DateUtils.getNowDate());
            boolean saved=dcCourseTestpaperService.save(testpaper);
            if (saved){
                List<DcCourseQuestion> questions=testpaper.getQuestions();
                for (DcCourseQuestion qt:questions
                ) {
                    qt.setPaperId(testpaper.getPaperId());
                    qt.setCreateTime(DateUtils.getNowDate());
                    qt.setCreateUser(userid);
                    boolean qtsaved=dcCourseQuestionService.save(qt);
                    if (qtsaved)
                    {
                        List<DcCourseQuestionItem> items=qt.getItems();
                        for (DcCourseQuestionItem item:items
                        )
                        {
                            item.setQuestionId(qt.getQuestionId());
                            item.setCreateTime(DateUtils.getNowDate());
                            item.setCreateUser(userid);
                            dcCourseQuestionItemService.save(item);
                        }
                    }


                }
            }
        }
        else {
            System.out.println("课程问卷调查 生产数量是零");
        }
    }
}
