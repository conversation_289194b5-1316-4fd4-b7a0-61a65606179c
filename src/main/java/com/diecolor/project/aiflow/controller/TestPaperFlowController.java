package com.diecolor.project.aiflow.controller;

import com.diecolor.framework.web.controller.BaseController;
import com.diecolor.project.aiflow.llm.QwenClientFlow;
import com.diecolor.project.aiflow.service.AutoQuestionFlowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;

@RestController
@RequestMapping("/scenario/ai/testpaperflow")
public class TestPaperFlowController extends BaseController {

    @Autowired
    private AutoQuestionFlowService service;


    @GetMapping(value = "/generate",produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter generate(
            @RequestParam(required = true) String sceneid,
            @RequestParam(required = true)  String topic,
            @RequestParam(value = "num",defaultValue = "10") Integer num) throws IOException {

        SseEmitter emitter = new SseEmitter();
        final String userid=getUserId().toString();
        QwenClientFlow.ResponseCallback callback = new QwenClientFlow.ResponseCallback() {

            @Override
            public void onPartialResponse(String partialResponse) {
                try {
                    emitter.send(SseEmitter.event().data(partialResponse));
                } catch (IOException e) {
                    emitter.completeWithError(e);
                }
            }

            @Override
            public void onComplete(String fullResponse) {
                service.saveSurvey(fullResponse,sceneid,userid);
                try {
                    emitter.send(SseEmitter.event().data("[OVER]"));
                    emitter.complete();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
            @Override
            public void onFailure(IOException e) {
                emitter.completeWithError(e);
            }
        };

        service.generateSurvey(topic,num,callback);
        return emitter;


    }


    @GetMapping("/generate/course")
    public SseEmitter generatecourseTestpaper(
            @RequestParam(required = true) String courseid,
            @RequestParam(required = true)  String topic,
            @RequestParam(value = "num",defaultValue = "10") Integer num) throws IOException {

        SseEmitter emitter = new SseEmitter();
        final String userid=getUserId().toString();
        QwenClientFlow.ResponseCallback callback = new QwenClientFlow.ResponseCallback() {

            @Override
            public void onPartialResponse(String partialResponse) {
                try {
                    emitter.send(SseEmitter.event().data(partialResponse));
                } catch (IOException e) {
                    emitter.completeWithError(e);
                }
            }

            @Override
            public void onComplete(String fullResponse) {
                service.saveCourseSurvey(fullResponse,courseid,userid);
                try {
                    emitter.send(SseEmitter.event().data("[OVER]"));
                    emitter.complete();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
            @Override
            public void onFailure(IOException e) {
                emitter.completeWithError(e);
            }
        };

        service.generateSurvey(topic,num,callback);
        return emitter;


    }

}
