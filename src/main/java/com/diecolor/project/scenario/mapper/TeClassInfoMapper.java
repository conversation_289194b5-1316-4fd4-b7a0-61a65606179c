package com.diecolor.project.scenario.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.diecolor.project.scenario.domain.TeClassInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 班级信息Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface TeClassInfoMapper extends BaseMapper<TeClassInfo> {

    /**
     * 根据学生用户名查询班级信息
     * 通过学生信息表关联查询班级信息
     *
     * @param studentCode 学生代码（用户名）
     * @return 班级信息
     */
    @Select("SELECT * FROM TE_CLASS_INFO tci WHERE tci.CLASS_CODE = (" +
            "SELECT student_classcode FROM ST_STUDENT_INFO ssi WHERE ssi.STUDENT_CODE = #{studentCode}" +
            ")")
    TeClassInfo selectClassInfoByStudentCode(@Param("studentCode") String studentCode);
}