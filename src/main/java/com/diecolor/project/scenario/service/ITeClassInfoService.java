package com.diecolor.project.scenario.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.diecolor.project.scenario.domain.TeClassInfo;

/**
 * 班级信息Service接口
 * 
 * <AUTHOR>
 */
public interface ITeClassInfoService extends IService<TeClassInfo> {

    /**
     * 根据学生用户名查询班级信息
     * 通过学生信息表关联查询班级信息
     *
     * @param studentCode 学生代码（用户名）
     * @return 班级信息
     */
    TeClassInfo getClassInfoByStudentCode(String studentCode);
}