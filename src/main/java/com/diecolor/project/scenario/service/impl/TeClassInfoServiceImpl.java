package com.diecolor.project.scenario.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.diecolor.project.scenario.domain.TeClassInfo;
import com.diecolor.project.scenario.mapper.TeClassInfoMapper;
import com.diecolor.project.scenario.service.ITeClassInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 班级信息Service实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class TeClassInfoServiceImpl extends ServiceImpl<TeClassInfoMapper, TeClassInfo> implements ITeClassInfoService {

    /**
     * 根据学生用户名查询班级信息
     * 通过学生信息表关联查询班级信息
     *
     * @param studentCode 学生代码（用户名）
     * @return 班级信息
     */
    @Override
    public TeClassInfo getClassInfoByStudentCode(String studentCode) {
        log.debug("根据学生代码查询班级信息，studentCode: {}", studentCode);
        
        try {
            TeClassInfo classInfo = baseMapper.selectClassInfoByStudentCode(studentCode);
            if (classInfo != null) {
                log.debug("查询到学生 {} 的班级信息: classCode={}, className={}", 
                    studentCode, classInfo.getClassCode(), classInfo.getClassName());
            } else {
                log.warn("未查询到学生 {} 的班级信息", studentCode);
            }
            return classInfo;
        } catch (Exception e) {
            log.error("根据学生代码查询班级信息异常，studentCode: {}", studentCode, e);
            return null;
        }
    }
}