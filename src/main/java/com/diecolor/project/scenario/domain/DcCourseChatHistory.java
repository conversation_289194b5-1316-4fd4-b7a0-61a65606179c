package com.diecolor.project.scenario.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.diecolor.framework.aspectj.lang.annotation.Excel;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 课程聊天历史记录对象 dc_course_chat_history
 */
@Data
@TableName("DC_COURSE_CHAT_HISTORY")
public class DcCourseChatHistory implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 聊天ID */
    @TableId(value = "CHAT_ID", type = IdType.ASSIGN_ID)
    @Excel(name = "聊天ID")
    private String chatId;

    /** 学号 */
    @TableField("ID_NUMBER")
    @Excel(name = "学号")
    private String idNumber;

    /** 课程ID */
    @TableField("COURSE_ID")
    @Excel(name = "课程ID")
    private String courseId;

    /** 群组ID */
    @TableField("GROUP_ID")
    @Excel(name = "群组ID")
    private String groupId;

    /** 聊天内容 */
    @TableField("CHAT_CONTENT")
    @Excel(name = "聊天内容")
    private String chatContent;

    /** 聊天时间 */
    @TableField(value = "CHAT_TIME", jdbcType = org.apache.ibatis.type.JdbcType.TIMESTAMP)
    @Excel(name = "聊天时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date chatTime;
} 