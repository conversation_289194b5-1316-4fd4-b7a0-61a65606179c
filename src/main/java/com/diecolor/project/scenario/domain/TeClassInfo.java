package com.diecolor.project.scenario.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 班级信息实体类
 * 对应表：TE_CLASS_INFO
 * 
 * <AUTHOR>
 */
@Data
@TableName("TE_CLASS_INFO")
public class TeClassInfo implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 资源ID - 主键
     */
    @TableId(value = "RESOURCE_ID", type = IdType.ASSIGN_ID)
    private String resourceId;

    /**
     * 班级计划ID
     */
    @TableField("CLASS_PLANID")
    private String classPlanId;

    /**
     * 班级编码
     */
    @TableField("CLASS_CODE")
    private String classCode;

    /**
     * 班级名称
     */
    @TableField("CLASS_NAME")
    private String className;

    /**
     * 年度
     */
    @TableField("CLASS_YEAR")
    private String classYear;

    /**
     * 学期
     */
    @TableField("CLASS_TERM")
    private String classTerm;

    /**
     * 报道人数
     */
    @TableField("CLASS_REGISTERNUMBER")
    private BigDecimal classRegisterNumber;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("CLASS_STARTTIME")
    private Date classStartTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("CLASS_ENDTIME")
    private Date classEndTime;

    /**
     * 天数
     */
    @TableField("CLASS_DAY")
    private BigDecimal classDay;

    /**
     * 班级类型
     */
    @TableField("CLASS_TYPE")
    private String classType;

    /**
     * 班级分类
     */
    @TableField("CLASS_CLASSIFY")
    private String classClassify;

    /**
     * 学员对象
     */
    @TableField("CLASS_TARGET")
    private String classTarget;

    /**
     * 班主任
     */
    @TableField("CLASS_HEADMASTER")
    private String classHeadmaster;

    /**
     * 跟班人
     */
    @TableField("CLASS_ATTENDANT")
    private String classAttendant;

    /**
     * 常用教室
     */
    @TableField("CLASS_CLASSROOM")
    private String classClassroom;

    /**
     * 餐厅
     */
    @TableField("CLASS_RESTAURANT")
    private String classRestaurant;

    /**
     * 教室安排状态
     */
    @TableField("CLASS_CLASSROOMSTATE")
    private String classClassroomState;

    /**
     * 餐厅安排状态
     */
    @TableField("CLASS_RESTAURANTSTATE")
    private String classRestaurantState;

    /**
     * 酒店安排状态
     */
    @TableField("CLASS_HOTELROOMSTATE")
    private String classHotelRoomState;

    /**
     * 上午上课时间
     */
    @TableField("CLASS_AMTIMESTART")
    private String classAmTimeStart;

    /**
     * 上午下课时间
     */
    @TableField("CLASS_AMTIMEEND")
    private String classAmTimeEnd;

    /**
     * 下午上课时间
     */
    @TableField("CLASS_PMTIMESTART")
    private String classPmTimeStart;

    /**
     * 下午下课时间
     */
    @TableField("CLASS_PMTIMEEND")
    private String classPmTimeEnd;

    /**
     * 晚上上课时间
     */
    @TableField("CLASS_EMTIMESTART")
    private String classEmTimeStart;

    /**
     * 晚上下课时间
     */
    @TableField("CLASS_EMTIMEEND")
    private String classEmTimeEnd;

    /**
     * 备注
     */
    @TableField("CLASS_REMARK")
    private String classRemark;

    /**
     * 排序号
     */
    @TableField("CLASS_ORDER")
    private BigDecimal classOrder;

    /**
     * 班级状态
     */
    @TableField("CLASS_STATE")
    private String classState;

    /**
     * 取消日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("CLASS_CANCELDATE")
    private Date classCancelDate;

    /**
     * 取消人
     */
    @TableField("CLASS_CANCELUSER")
    private String classCancelUser;

    /**
     * 取消原因
     */
    @TableField("CLASS_CANCELRESON")
    private String classCancelReason;

    /**
     * 教学计划状态
     */
    @TableField("CLASS_PLANSTATE")
    private String classPlanState;

    /**
     * 所属学校
     */
    @TableField("CLASS_ATTACHPARTSCHOOL")
    private String classAttachPartSchool;

    /**
     * 创建人
     */
    @TableField("CREATE_USER")
    private String createUser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("CREATE_TIME")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("MODIFY_USER")
    private String modifyUser;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("MODIFY_TIME")
    private Date modifyTime;

    /**
     * 班级计划人数
     */
    @TableField("CLASS_PLANNUMBER")
    private String classPlanNumber;

    /**
     * 经费使用状态
     */
    @TableField("CLASS_FUNDSTATE")
    private String classFundState;

    /**
     * 教室安排备注
     */
    @TableField("CLASS_CLASSROOMDESCRIBE")
    private String classClassroomDescribe;

    /**
     * 餐厅安排备注
     */
    @TableField("CLASS_RESTAURANTDESCRIBE")
    private String classRestaurantDescribe;

    /**
     * 课程状态 0 未发布 1 已发布
     */
    @TableField("CLASS_COURSESTATE")
    private String classCourseState;

    /**
     * 教室的最大行数
     */
    @TableField("CLASS_SEATROW")
    private String classSeatRow;

    /**
     * 教室的最大列数
     */
    @TableField("CLASS_SEATCLOUMN")
    private String classSeatColumn;

    /**
     * 委培单位
     */
    @TableField("CLASS_ENTRUSTUNIT")
    private String classEntrustUnit;

    /**
     * 教学计划发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("CLASS_PLANRELEASETIME")
    private Date classPlanReleaseTime;

    /**
     * 所属管理部门
     */
    @TableField("CLASS_CREATEUNIT")
    private String classCreateUnit;

    /**
     * 可请假天数
     */
    @TableField("CLASS_CAN_LEAVE_DAYS")
    private BigDecimal classCanLeaveDays;

    /**
     * 工作人员
     */
    @TableField("CLASS_STAFF")
    private String classStaff;

    /**
     * 备注2
     */
    @TableField("CLASS_REMARK2")
    private String classRemark2;

    /**
     * 附件路径
     */
    @TableField("CLASS_AFFIXPATH")
    private String classAffixPath;

    /**
     * 附件名
     */
    @TableField("CLASS_AFFIXNAME")
    private String classAffixName;

    /**
     * 报到地点
     */
    @TableField("CLASS_REPORTEDLOCATION")
    private String classReportedLocation;

    /**
     * 跟班人电话
     */
    @TableField("CLASS_ATTENDANTPHONE")
    private String classAttendantPhone;

    /**
     * 是否结项
     */
    @TableField("CLASS_ISEND")
    private String classIsEnd;

    /**
     * 带班部门
     */
    @TableField("CLASS_HEADMASTERUNIT")
    private String classHeadmasterUnit;

    /**
     * 班级简称
     */
    @TableField("CLASS_SHORTNAME")
    private String classShortName;

    /**
     * 教务联系员
     */
    @TableField("CLASS_CONTACTS")
    private String classContacts;

    /**
     * 组织部班级ID
     */
    @TableField("ZZB_CLASSID")
    private String zzbClassId;

    /**
     * 班级安排申请ID
     */
    @TableField("CLASS_ARRANGEAPPLYID")
    private String classArrangeApplyId;

    /**
     * 决策部署
     */
    @TableField("CLASS_DECISION_DEPLOYMENT")
    private String classDecisionDeployment;

    /**
     * 教学督导
     */
    @TableField("CLASS_SUPERVISION")
    private String classSupervision;

    /**
     * 培训方式
     */
    @TableField("CLASS_TRAINTYPE")
    private String classTrainType;

    /**
     * 是否专职
     */
    @TableField("CLASS_ISZZD")
    private String classIsZzd;
}