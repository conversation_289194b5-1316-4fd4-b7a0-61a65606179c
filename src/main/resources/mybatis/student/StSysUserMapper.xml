<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.diecolor.project.student.mapper.StSysUserMapper">
    
    <select id="selectStudentCourseInfo" resultType="com.diecolor.project.student.domain.dto.StudentCourseInfoDTO">
        SELECT
            dc.COURSE_ID           AS courseId,
            dc.COURSE_NAME         AS courseName,
            dc.COURSE_INTRODUCTION AS courseIntroduction,
            dc.SCENE_ID            AS sceneId,
            ds.SCENE_NAME           AS sceneName,
            ds.SCENE_INTRODUCTION  AS sceneIntroduction
        FROM DC_COURSE dc
        LEFT JOIN DC_SCENE ds ON dc.SCENE_ID = ds.SCENE_ID
        WHERE dc.ISDELETE != '1'
          AND dc.CLASS_CODE IN (
                SELECT ssi.STUDENT_CLASSCODE
                FROM ST_STUDENT_INFO ssi
                WHERE ssi.STUDENT_CODE = #{userName}
            )
    </select>
    
    <select id="selectStudentCourseGroupInfo" resultType="com.diecolor.project.student.domain.dto.StudentCourseInfoDTO">
        SELECT
            s.STUDENT_CODE AS userName,
            p.PUPPET_NAME AS nickName,
            p.PUPPET_ICON AS avatar,
            g.GROUP_ID AS groupId,
            g.GROUP_NAME AS groupName,
            p.PUPPET_NAME AS puppetName,
            p.PUPPET_ICON AS puppetIcon,
            c.COURSE_ID AS courseId,
            c.COURSE_NAME AS courseName,
            c.COURSE_INTRODUCTION AS courseIntroduction,
            sc.SCENE_ID AS sceneId,
            sc.SCENE_NAME AS sceneName,
            sc.SCENE_INTRODUCTION AS sceneIntroduction,
            CASE WHEN s.IS_GROUP_LEADER = 1 THEN 1 ELSE 0 END AS isGroupLeader
        FROM DC_COURSE_STUDENT s
        LEFT JOIN DC_COURSE_PUPPET p ON s.PUPPET_ID = p.PUPPET_ID
        LEFT JOIN DC_COURSE_GROUP g ON p.GROUP_ID = g.GROUP_ID
        LEFT JOIN DC_COURSE c ON g.COURSE_ID = c.COURSE_ID
        LEFT JOIN DC_SCENE sc ON c.SCENE_ID = sc.SCENE_ID
        WHERE s.STUDENT_CODE = #{userName}
          AND (c.ISDELETE IS NULL OR c.ISDELETE = '0')
          AND sc.ISDELETE = '0'
        ORDER BY g.GROUP_ORDER, s.STUDENT_INDEX
    </select>

    <select id="selectCourseUsersByClassCode" resultType="com.diecolor.project.student.dto.GroupUserDTO">
        SELECT
            su.user_id,
            su.user_name,
            su.nick_name,
            su.avatar
        FROM
            sys_user su
        LEFT JOIN st_student_info ssi ON su.user_name = ssi.student_code
        WHERE su.del_flag = '0' AND su.status = '0'
        AND ssi.student_classcode = (select d.class_code from dc_course d where d.course_id = #{courseId})
    </select>

    <!-- 根据课程ID查询课程群的成员列表 -->
    <select id="selectCourseMembersByCourseId" resultType="com.diecolor.project.student.dto.GroupUserDTO">
        SELECT
            su.user_id,
            su.user_name,
            su.nick_name,
            su.avatar
        FROM
            sys_user su
        LEFT JOIN
            st_student_info ssi ON ssi.student_code = su.user_name
        WHERE
            su.del_flag = '0' AND su.status = '0' AND
            ssi.student_classcode = (
                SELECT c.class_code FROM dc_course c WHERE c.course_id = #{courseId}
            )
    </select>

    <!-- 根据分组ID查询分组的成员列表（包含马甲信息） -->
    <select id="selectGroupMembersByGroupId" resultType="com.diecolor.project.student.dto.GroupUserDTO">
        SELECT
            u.user_id,
            cs.student_code AS user_name,
            u.nick_name AS nick_name,
            u.avatar,
            cp.puppet_name AS puppet_name,
            cp.puppet_icon AS puppet_icon,
            cs.is_group_leader
        FROM dc_course_group g
        INNER JOIN dc_course_puppet cp ON g.group_id = cp.group_id
        INNER JOIN dc_course_student cs ON cp.puppet_id = cs.puppet_id
        LEFT JOIN sys_user u ON cs.student_code = u.user_name
        WHERE g.group_id = #{groupId}
          AND u.del_flag = '0'
          AND u.status = '0'
        ORDER BY cs.is_group_leader DESC, cs.student_index ASC
    </select>

    <!-- 查询学生进行中的课程信息 -->
    <select id="selectStudentActiveCourseInfo" resultType="com.diecolor.project.student.domain.dto.StudentCourseInfoDTO">
        SELECT
            dc.COURSE_ID           AS courseId,
            dc.COURSE_NAME         AS courseName,
            dc.COURSE_INTRODUCTION AS courseIntroduction
        FROM DC_COURSE dc
        WHERE dc.ISDELETE != '1'
          AND dc.COURSE_STATE = '1'
          AND dc.CLASS_CODE IN (
                SELECT ssi.STUDENT_CLASSCODE
                FROM ST_STUDENT_INFO ssi
                WHERE ssi.STUDENT_CODE = #{userName}
            )
        ORDER BY dc.CREATE_TIME DESC
    </select>

    <!-- 查询学生所有的课程信息（不限制状态） -->
    <select id="selectStudentAllCourseInfo" resultType="com.diecolor.project.student.domain.dto.StudentCourseInfoDTO">
        SELECT
            dc.COURSE_ID           AS courseId,
            dc.COURSE_NAME         AS courseName,
            dc.COURSE_INTRODUCTION AS courseIntroduction,
            dc.SCENE_ID            AS sceneId,
            dc.COURSE_BEGIN_TIME   AS startTime,
            dc.COURSE_END_TIME     AS endTime,
            dc.COURSE_STATE        AS courseState
        FROM DC_COURSE dc
        WHERE dc.ISDELETE != '1'
          AND dc.CLASS_CODE IN (
                SELECT ssi.STUDENT_CLASSCODE
                FROM ST_STUDENT_INFO ssi
                WHERE ssi.STUDENT_CODE = #{userName}
            )
        ORDER BY dc.CREATE_TIME DESC
    </select>

    <!-- 查询当前用户所在班级的所有成员 -->
    <select id="selectClassMembersByUserName" resultType="com.diecolor.project.student.dto.GroupUserDTO">
        SELECT
            su.user_id,
            su.user_name,
            su.nick_name,
            su.avatar
        FROM SYS_USER su
        LEFT JOIN ST_STUDENT_INFO ssi ON ssi.STUDENT_CODE = su.USER_NAME
        WHERE ssi.STUDENT_CLASSCODE IN (
            SELECT student_classcode FROM ST_STUDENT_INFO ssi WHERE ssi.STUDENT_CODE = #{userName}
        )
        AND su.del_flag = '0' AND su.status = '0'
    </select>

    <select id="selectUsersByClassCode" resultType="com.diecolor.project.student.dto.GroupUserDTO">
        SELECT
            su.user_id,
            su.user_name,
            su.nick_name,
            su.avatar
        FROM SYS_USER su
                 LEFT JOIN ST_STUDENT_INFO ssi ON ssi.STUDENT_CODE = su.USER_NAME
        WHERE ssi.STUDENT_CLASSCODE = #{classCode}
        AND su.del_flag = '0' AND su.status = '0'
    </select>

    <!-- 根据部门ID查询部门下的所有学生用户 -->
    <select id="selectUsersByDeptId" resultType="com.diecolor.project.student.dto.GroupUserDTO">
        SELECT
            su.user_id,
            su.user_name,
            su.nick_name,
            su.avatar
        FROM SYS_USER su
        WHERE su.dept_id = #{deptId}
        AND su.del_flag = '0' AND su.status = '0'
        ORDER BY su.user_name
    </select>

</mapper>