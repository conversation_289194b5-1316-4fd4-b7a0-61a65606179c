<script>
	import { checkAndAutoLogin } from '@/utils/wechatAuth';
	import { globalWebSocketManager } from '@/utils/global-websocket.js';

	export default {
		onLaunch: async function() {
			console.log('App Launch');
			
			// 使用封装的检查和自动登录函数
			const loginSuccess = await checkAndAutoLogin();
			
			if (loginSuccess) {
				console.log('自动登录成功');
			} else {
				console.log('自动登录失败，将在页面加载时跳转到登录页面');
				// 不在这里直接跳转，而是让页面的 onLoad 处理跳转
				// 避免与页面路由冲突
			}
		},
		onShow: function() {
			console.log('App Show - Application returned to foreground');
			
			// 安全地记录应用前台状态
			try {
				const app = getApp();
				if (app) {
					app.globalData = app.globalData || {};
					app.globalData.appInForeground = true;
					app.globalData.lastForegroundTime = Date.now();
				} else {
					console.warn('getApp() returned undefined in onShow');
				}
			} catch (error) {
				console.error('Error in onShow:', error);
			}
		},
		onHide: function() {
			console.log('App Hide - Application went to background');
			
			// 安全地记录应用后台状态
			try {
				const app = getApp();
				if (app) {
					app.globalData = app.globalData || {};
					app.globalData.appInForeground = false;
					app.globalData.lastBackgroundTime = Date.now();
				} else {
					console.warn('getApp() returned undefined in onHide');
				}
			} catch (error) {
				console.error('Error in onHide:', error);
			}
			
			// 当应用进入后台时，WebSocket连接通常会断开
			// 这里可以添加一些清理逻辑，但主要的重连逻辑在页面的onShow中处理
		},
		onUnload: function() {
			console.log('App Unload - Application is being terminated');
			
			// 应用完全退出时断开WebSocket连接
			try {
				globalWebSocketManager.disconnect();
				console.log('Global WebSocket disconnected on app termination');
			} catch (error) {
				console.error('Error disconnecting WebSocket on app termination:', error);
			}
		}
	}
</script>

<style>
	/*每个页面公共css */
</style>
