<template>
  <view class="course-page">
    <!-- 顶部背景区域 -->
    <view class="top-section" :style="{ paddingTop: statusBarHeight + 'px' }">
      <image class="bg-image" src="/static/course/c1-top-bg.png" mode="aspectFill"></image>
      
      <!-- 欢迎信息 -->
      <view class="welcome-content">
        <text class="welcome-title">Hi，{{ userInfo.nickName || '同学' }}</text>
        <text class="welcome-subtitle">欢迎您使用情景课堂</text>
      </view>
    </view>

    <!-- 主要功能卡片 -->
    <view class="main-cards">
      <view class="card-item" @click="handleGroupDiscussion">
        <image class="card-icon" src="/static/course/c1-puppet-group.png"></image>
        <view class="card-content">
          <text class="card-title">群组讨论</text>
          <text class="card-subtitle">立即进入</text>
        </view>
      </view>

      <view class="card-item" @click="handleClassGroup">
        <image class="card-icon" src="/static/course/c1-class-group.png"></image>
        <view class="card-content">
          <text class="card-title">班级大群</text>
          <text class="card-subtitle">立即进入</text>
        </view>
      </view>
    </view>
    
    <!-- 功能按钮区域 -->
    <view class="function-section">
      <view class="function-row">
        <view class="function-item" @click="handleCourseIntro">
          <view class="function-icon course-icon">
            <image src="/static/course/c1-course.png"></image>
          </view>
          <view class="function-text">
            <text class="function-title">课程简介</text>
            <text class="function-subtitle">详细介绍</text>
          </view>
        </view>
        
        <view class="function-item" @click="handleSurvey">
          <view class="function-icon survey-icon">
            <image src="/static/course/c1-paper.png"></image>
          </view>
          <view class="function-text">
            <text class="function-title">问卷调查</text>
            <text class="function-subtitle">立即使用</text>
          </view>
        </view>
        
        <view class="function-item" @click="handleTask">
          <view class="function-icon task-icon">
            <image src="/static/course/c1-task.png"></image>
          </view>
          <view class="function-text">
            <text class="function-title">课程任务</text>
            <text class="function-subtitle">参与任务</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 我的课程区域 -->
    <view class="course-section">
      <view class="section-header">
        <text class="section-title">我的课程</text>
      </view>
      
      <view class="course-list-container">
        <view 
          v-for="course in myCourseList" 
          :key="course.courseId"
          class="course-card"
          @click="enterCourse(course)"
        >
          <text class="course-name">{{ course.courseName }}</text>
          <view class="course-meta">
            <text class="teacher-info">{{ course.teacherName || '李教授' }} | {{ formatTime(course.startTime) || '7月20日 09:00' }}</text>
          </view>
          
          <view class="course-status">
            <view class="status-dot" :class="getStatusDotClass(course.status)"></view>
            <text class="status-text">{{ course.currentStage || '' }}</text>
            <view class="status-button" :class="getStatusButtonClass(course.status)">
              <text>{{ getStatusButtonText(course.status) }}</text>
            </view>
          </view>
        </view>
        
        <!-- 空状态 -->
        <view v-if="myCourseList.length === 0 && !loading" class="empty-course-state">
          <text class="empty-text">暂无课程</text>
        </view>
      </view>

      <wd-gap :height="20" />
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { onLoad, onUnload, onShow, onHide } from '@dcloudio/uni-app';
import { useUserStore } from '@/stores/user';
import config from '@/utils/config';
import { useGlobalWebSocket } from '@/utils/global-websocket.js';

// 状态管理
const userStore = useUserStore();
const statusBarHeight = ref(0);
const userInfo = ref({});
const courseInfo = ref(null);
const myCourseList = ref([]);
const loading = ref(false);

// WebSocket 全局管理
const globalWebSocket = useGlobalWebSocket();
const websocketInitialized = ref(false);
const currentCourseId = ref(null);

// 页面加载时执行
onLoad((options) => {
  console.log('Course Page loaded with options:', options);
  
  // 检查用户登录状态
  if (!userStore.isLoggedIn) {
    console.log('User not logged in, redirecting to login...');
    uni.reLaunch({
      url: '/pages/login/index'
    });
    return;
  }
  
  // 获取系统信息
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight || 44;
  
  // 保存当前课程ID
  if (options.courseId) {
    currentCourseId.value = options.courseId;
    loadCourseInfo(options.courseId);
  }
  
  loadUserInfo();
  loadMyCourseList();
  
  // 初始化WebSocket连接
  initializeWebSocketConnection();
});

// 页面卸载时执行
onUnload(() => {
  console.log('Course Page unloading...');
  // 注意：这里不断开WebSocket连接，让它在全局保持
  // 只有应用完全退出时才断开连接
});

// 页面显示时执行
onShow(() => {
  console.log('Course Page shown');
  // 检查WebSocket连接状态，如果需要则重新连接
  checkAndReconnectWebSocket();
});

// 页面隐藏时执行
onHide(() => {
  console.log('Course Page hidden');
  // WebSocket连接保持，不做特殊处理
});

// 加载用户信息
const loadUserInfo = async () => {
  try {
    const response = await new Promise((resolve, reject) => {
      uni.request({
        url: config.BASE_URL + '/student/user/info',
        method: 'GET',
        header: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + userStore.token
        },
        success: (res) => {
          if (res.statusCode === 200) {
            const responseData = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
            resolve(responseData);
          } else {
            reject(new Error(`HTTP错误 ${res.statusCode}`));
          }
        },
        fail: reject
      });
    });

    if (response && response.code === 200) {
      userInfo.value = response.data || {};
    }
  } catch (error) {
    console.error('加载用户信息失败:', error);
    // 使用默认用户信息
    userInfo.value = { nickName: userStore.userInfo?.nickName || '同学' };
  }
};

// 加载课程信息
const loadCourseInfo = async (courseId) => {
  try {
    const response = await new Promise((resolve, reject) => {
      uni.request({
        url: config.BASE_URL + `/student/course/info/${courseId}`,
        method: 'GET',
        header: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + userStore.token
        },
        success: (res) => {
          if (res.statusCode === 200) {
            const responseData = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
            resolve(responseData);
          } else {
            reject(new Error(`HTTP错误 ${res.statusCode}`));
          }
        },
        fail: reject
      });
    });

    if (response && response.code === 200) {
      courseInfo.value = response.data || {};
    }
  } catch (error) {
    console.error('加载课程信息失败:', error);
    // 使用默认课程信息
    courseInfo.value = {
      courseName: '2025年春季学期第二阶段',
      teacherName: '李教授',
      startTime: new Date(),
      currentStage: '第二阶段-第3节',
      status: 'upcoming'
    };
  }
};

// 格式化时间
const formatTime = (time) => {
  if (!time) return '';
  const date = new Date(time);
  return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
};

// 加载我的课程列表
const loadMyCourseList = async () => {
  try {
    loading.value = true;
    const response = await new Promise((resolve, reject) => {
      uni.request({
        url: config.BASE_URL + '/student/course/my-courses',
        method: 'GET',
        header: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + userStore.token
        },
        success: (res) => {
          if (res.statusCode === 200) {
            const responseData = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
            resolve(responseData);
          } else {
            reject(new Error(`HTTP错误 ${res.statusCode}`));
          }
        },
        fail: reject
      });
    });

    if (response && response.code === 200) {
      myCourseList.value = response.data || [];
    }
  } catch (error) {
    console.error('加载我的课程列表失败:', error);
    myCourseList.value = [];
  } finally {
    loading.value = false;
  }
};

// 获取状态按钮文本
const getStatusButtonText = (status) => {
  switch (status) {
    case 'ongoing': return '正在进行';
    case 'upcoming': return '即将开始';
    case 'ended': return '已结束';
    default: return '即将开始';
  }
};

// 获取状态点的样式类
const getStatusDotClass = (status) => {
  switch (status) {
    case 'ongoing': return 'status-dot-ongoing';
    case 'upcoming': return 'status-dot-upcoming';
    case 'ended': return 'status-dot-ended';
    default: return 'status-dot-upcoming';
  }
};

// 获取状态按钮的样式类
const getStatusButtonClass = (status) => {
  switch (status) {
    case 'ongoing': return 'status-button-ongoing';
    case 'upcoming': return 'status-button-upcoming';
    case 'ended': return 'status-button-ended';
    default: return 'status-button-upcoming';
  }
};

// 功能入口方法
const handleGroupDiscussion = () => {
  // 检查WebSocket连接状态
  if (!globalWebSocket.isConnected.value) {
    console.log('[CourseIndex] WebSocket not connected, navigating directly');
    // 直接跳转，让index页面处理WebSocket连接
    uni.navigateTo({
      url: '/pages/index/index?chatType=group'
    });
    return;
  }
  
  // 跳转到聊天页面，指定群组聊天室类型
  globalWebSocket.navigateToPage('/pages/index/index');
  uni.navigateTo({
    url: '/pages/index/index?chatType=group'
  });
};

const handleClassGroup = () => {
  // 检查WebSocket连接状态
  if (!globalWebSocket.isConnected.value) {
    console.log('[CourseIndex] WebSocket not connected, navigating directly');
    // 直接跳转，让index页面处理WebSocket连接
    uni.navigateTo({
      url: '/pages/index/index?chatType=class'
    });
    return;
  }
  
  // 跳转到聊天页面，指定班级大群类型
  globalWebSocket.navigateToPage('/pages/index/index');
  uni.navigateTo({
    url: '/pages/index/index?chatType=class'
  });
};

const handleCourseIntro = () => {
  uni.showToast({
    title: '课程简介功能开发中',
    icon: 'none'
  });
};

const handleSurvey = () => {
  uni.showToast({
    title: '问卷调查功能开发中',
    icon: 'none'
  });
};

const handleTask = () => {
  uni.showToast({
    title: '课程任务功能开发中',
    icon: 'none'
  });
};

const handleJoinClass = () => {
  uni.showToast({
    title: '进入课堂功能开发中',
    icon: 'none'
  });
};

// 进入课程
const enterCourse = (course) => {
  // 可以重新加载当前页面或者跳转到相同页面但参数不同
  uni.redirectTo({
    url: `/pages/course/index?courseId=${course.courseId}&courseName=${encodeURIComponent(course.courseName)}`
  });
};

// 初始化WebSocket连接
const initializeWebSocketConnection = async () => {
  if (websocketInitialized.value) {
    console.log('[Course Page] WebSocket already initialized');
    return;
  }
  
  try {
    console.log('[Course Page] Initializing WebSocket connection...');
    
    // 获取群组信息用于WebSocket连接
    const groups = await fetchGroupsForWebSocket();
    
    if (groups && groups.length > 0) {
      console.log('[Course Page] Groups fetched, initializing global WebSocket...');
      
      // 初始化全局WebSocket连接
      await globalWebSocket.initialize(
        groups,
        handleWebSocketMessage,
        '/pages/course/index'
      );
      
      websocketInitialized.value = true;
      console.log('[Course Page] WebSocket initialized successfully');
    } else {
      console.warn('[Course Page] No groups available for WebSocket connection');
      // 可以设置一个定时器稍后重试
      setTimeout(() => {
        if (!websocketInitialized.value) {
          initializeWebSocketConnection();
        }
      }, 5000);
    }
  } catch (error) {
    console.error('[Course Page] Failed to initialize WebSocket:', error);
    // 可以设置一个定时器稍后重试
    setTimeout(() => {
      if (!websocketInitialized.value) {
        initializeWebSocketConnection();
      }
    }, 5000);
  }
};

// 检查并重新连接WebSocket
const checkAndReconnectWebSocket = () => {
  if (globalWebSocket.needsReinitialization()) {
    console.log('[Course Page] WebSocket needs reinitialization');
    websocketInitialized.value = false;
    initializeWebSocketConnection();
  }
};

// 获取群组信息用于WebSocket连接
const fetchGroupsForWebSocket = async () => {
  try {
    // 获取群组聊天室信息
    const groupResponse = await new Promise((resolve, reject) => {
      uni.request({
        url: config.BASE_URL + '/student/chat/currentUser/courseInfo',
        method: 'GET',
        header: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + userStore.token
        },
        success: (res) => {
          if (res.statusCode === 200) {
            const responseData = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
            resolve(responseData);
          } else {
            reject(new Error(`HTTP错误 ${res.statusCode}`));
          }
        },
        fail: reject
      });
    });

    // 获取班级信息（可选功能，如果接口不存在则跳过）
    let classResponse = null;
    try {
      classResponse = await new Promise((resolve, reject) => {
        uni.request({
          url: config.BASE_URL + '/student/user/classInfo',
          method: 'GET',
          header: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + userStore.token
          },
          success: (res) => {
            if (res.statusCode === 200) {
              const responseData = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
              resolve(responseData);
            } else {
              console.warn('[Course Page] Class info API returned:', res.statusCode);
              resolve(null); // 不抛错，返回null
            }
          },
          fail: (err) => {
            console.warn('[Course Page] Class info API failed:', err);
            resolve(null); // 不抛错，返回null
          }
        });
      });
    } catch (error) {
      console.warn('[Course Page] Failed to fetch class info (API may not exist):', error);
      classResponse = null;
    }

    let fetchedGroups = [];

    // 处理群组聊天室
    if (groupResponse && groupResponse.code === 200 && Array.isArray(groupResponse.data)) {
      const groupChats = groupResponse.data
        .map(dto => ({
          id: dto.groupId ? String(dto.groupId) : (dto.teachingClassId ? String(dto.teachingClassId) : null),
          name: dto.groupName || dto.teachingClassName || '未知群组',
          type: 'group',
          isClass: false,
          isCourse: dto.isCourse || false,
          courseId: dto.courseId || '',
          sceneId: dto.sceneId || '',
          puppetName: dto.puppetName || '',
          puppetIcon: dto.puppetIcon || '',
        }))
        .filter(g => g.id);
      
      fetchedGroups = fetchedGroups.concat(groupChats);
    }

    // 处理班级大群
    if (classResponse && classResponse.code === 200 && classResponse.data) {
      const classInfo = classResponse.data;
      if (classInfo.classCode) {
        const classGroup = {
          id: String(classInfo.classCode),
          name: `${classInfo.className || '班级'}大群`,
          type: 'class',
          isClass: true,
          isCourse: false,
          courseId: '',
          sceneId: '',
          puppetName: userInfo.value?.nickName || userStore.userInfo?.nickName || userStore.userInfo?.username || '同学',
          puppetIcon: userInfo.value?.avatar || userStore.userInfo?.avatar || '',
          classCode: classInfo.classCode,
          className: classInfo.className
        };
        fetchedGroups.unshift(classGroup); // 班级大群放在前面
      }
    }

    console.log('[Course Page] Fetched groups:', fetchedGroups);
    return fetchedGroups;
  } catch (error) {
    console.error('[Course Page] Failed to fetch groups for WebSocket:', error);
    return [];
  }
};

// 处理WebSocket消息
const handleWebSocketMessage = (message) => {
  console.log('[Course Page] Received WebSocket message:', message);
  
  // 在课程首页，可以显示全局通知
  if (message.type === 'NOTIFICATION' || message.type === 'SYSTEM') {
    uni.showToast({
      title: message.content || '收到新消息',
      icon: 'none',
      duration: 2000
    });
  }
  
  // 可以在这里处理其他类型的实时通知
};
</script>

<style lang="scss" scoped>
.course-page {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.top-section {
  position: relative;
  height: 380rpx;
  overflow: hidden;
  border-radius: 0 0 60rpx 60rpx;
}

.bg-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.welcome-content {
  position: relative;
  z-index: 2;
  padding: 120rpx 40rpx 0;
}

.welcome-title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 16rpx;
}

.welcome-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
}

.main-cards {
  position: relative;
  z-index: 2;
  display: flex;
  gap: 32rpx;
  padding: 60rpx 40rpx 40rpx;
  margin-top: -120rpx;
}

.card-item {
  flex: 1;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 0 10rpx 0 rgba(0, 0, 0, 0.1);
  padding: 32rpx 24rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.card-icon {
  width: 64rpx;
  height: 64rpx;
  flex-shrink: 0;
}

.card-content {
  flex: 1;
}

.card-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.card-subtitle {
  display: block;
  font-size: 24rpx;
  color: #999999;
}

.function-section {
  padding: 0 40rpx;
  margin-bottom: 40rpx;
}

.function-row {
  display: flex;
  gap: 16rpx;
}

.function-item {
  flex: 1;
  background: #ffffff;
  border-radius: 16rpx;
  padding: 22rpx 12rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  text-align: left;
  gap: 18rpx;
  border: 1rpx solid #f0f0f0;
}

.function-icon {
  width: 46rpx;
  height: 46rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

// .function-icon.course-icon {
//   background: #FF6B6B;
// }

// .function-icon.survey-icon {
//   background: #4ECDC4;
// }

// .function-icon.task-icon {
//   background: #A8E6CF;
// }

.function-icon image {
  width: 100%;
  height: 100%;
}

.function-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.function-title {
  font-size: 24rpx;
  font-weight: 600;
  color: #333333;
}

.function-subtitle {
  font-size: 20rpx;
  color: #999999;
}

.course-section {
  padding: 0 40rpx;
}

.section-header {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  position: relative;
  z-index: 2;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -2rpx;
  left: 0;
  width: 100%;
  height: 18rpx;
  background: linear-gradient(to right, #ffffff, #F7AFAA);
  border-radius: 0 18rpx 18rpx 0;
  z-index: -1;
}

.course-list-container {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.course-card {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 32rpx;
  border: 1rpx solid #f0f0f0;
}

.course-name {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 24rpx;
}

.course-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.teacher-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
}

.teacher-info {
  font-size: 24rpx;
  color: #666666;
}

.course-status {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.status-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
}

.status-dot-ongoing {
  background: #4CAF50;
}

.status-dot-upcoming {
  background: #BD0407;
}

.status-dot-ended {
  background: #999999;
}

.status-text {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.status-button {
  padding: 6rpx 32rpx;
  border-radius: 32rpx;
  border: 2rpx solid;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-button-ongoing {
  background: #4CAF50;
  border-color: #4CAF50;
}

.status-button-upcoming {
  background: #BD0407;
  border-color: #BD0407;
}

.status-button-ended {
  background: #999999;
  border-color: #999999;
}

.status-button text {
  font-size: 24rpx;
  color: #ffffff;
}

.empty-course-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
}
</style>