<template>
  <view class="login-container">
    <!-- 状态栏占位 -->
    <view style="height: calc(var(--status-bar-height) + 44px);"></view>
    
    <!-- 登录内容区域 -->
    <view class="login-content">
      <!-- Logo区域 -->
      <view class="logo-section">
        <view class="logo-icon">
          <text class="logo-text">情景课堂</text>
        </view>
        <text class="app-slogan">智慧教学，精彩互动</text>
      </view>
      
      <!-- 登录说明 -->
      <view class="login-description">
        <text class="description-text">请授权微信登录以使用情景课堂功能</text>
      </view>
      
      <!-- 登录按钮 -->
      <view class="login-actions">
        <button 
          class="login-button" 
          :class="{ 'loading': isLogging }"
          :disabled="isLogging"
          @click="handleWechatLogin"
        >
          <wd-icon v-if="!isLogging" name="wechat-fill" size="20" color="#fff" />
          <wd-loading v-if="isLogging" size="20" color="#fff" />
          <text class="button-text">{{ isLogging ? '登录中...' : '微信授权登录' }}</text>
        </button>
      </view>
      
      <!-- 隐私说明 -->
      <view class="privacy-notice">
        <text class="privacy-text">登录即表示您同意我们的隐私政策</text>
        <text class="privacy-text">我们承诺保护您的个人信息安全</text>
      </view>
    </view>
    
    <!-- 底部装饰 -->
    <view class="bottom-decoration">
      <view class="decoration-circle circle-1"></view>
      <view class="decoration-circle circle-2"></view>
      <view class="decoration-circle circle-3"></view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { wechatLogin } from '@/utils/wechatAuth';
import { useUserStore } from '@/stores/user';

const isLogging = ref(false);
const userStore = useUserStore();

// 处理微信登录
const handleWechatLogin = async () => {
  if (isLogging.value) return;
  
  isLogging.value = true;
  
  try {
    const result = await wechatLogin(true);
    
    if (result.success) {
      // 登录成功，跳转到课程列表页面
      uni.reLaunch({
        url: '/pages/course-list/index'
      });
    }
  } catch (error) {
    console.error('登录失败:', error);
  } finally {
    isLogging.value = false;
  }
};

// 页面加载时检查是否已登录
onLoad(() => {
  // 如果已经登录，直接跳转到课程列表页面
  if (userStore.isLoggedIn) {
    uni.reLaunch({
      url: '/pages/course-list/index'
    });
    return;
  }
});
</script>

<style scoped>
.login-container {
  height: 100vh;
  background: linear-gradient(135deg, #BD0407 0%, #E84D53 50%, #BD0407 100%);
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.login-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0 40px;
  position: relative;
  z-index: 2;
}

.logo-section {
  text-align: center;
  margin-bottom: 60px;
}

.logo-icon {
  width: 120px;
  height: 120px;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.logo-text {
  font-size: 24px;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.app-slogan {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 300;
  letter-spacing: 1px;
}

.login-description {
  text-align: center;
  margin-bottom: 50px;
}

.description-text {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

.login-actions {
  width: 100%;
  max-width: 280px;
  margin-bottom: 40px;
}

.login-button {
  width: 100%;
  height: 50px;
  background-color: rgba(255, 255, 255, 0.95);
  color: #BD0407;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.login-button:not([disabled]):active {
  transform: translateY(2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.login-button.loading {
  background-color: rgba(255, 255, 255, 0.7);
  cursor: not-allowed;
}

.login-button[disabled] {
  opacity: 0.7;
}

.button-text {
  color: #BD0407;
  font-size: 16px;
  font-weight: 600;
}

.privacy-notice {
  text-align: center;
  max-width: 300px;
}

.privacy-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.8;
  display: block;
  margin-bottom: 4px;
}

.bottom-decoration {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 200px;
  pointer-events: none;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(5px);
}

.circle-1 {
  width: 100px;
  height: 100px;
  bottom: -20px;
  left: 20px;
  animation: float 6s ease-in-out infinite;
}

.circle-2 {
  width: 150px;
  height: 150px;
  bottom: -50px;
  right: -30px;
  animation: float 8s ease-in-out infinite reverse;
}

.circle-3 {
  width: 80px;
  height: 80px;
  bottom: 100px;
  right: 60px;
  animation: float 7s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 兼容不同屏幕尺寸 */
@media screen and (max-height: 600px) {
  .login-content {
    padding: 0 30px;
  }
  
  .logo-section {
    margin-bottom: 40px;
  }
  
  .logo-icon {
    width: 100px;
    height: 100px;
    border-radius: 50px;
  }
  
  .logo-text {
    font-size: 20px;
  }
  
  .login-description {
    margin-bottom: 30px;
  }
  
  .login-actions {
    margin-bottom: 30px;
  }
}
</style> 