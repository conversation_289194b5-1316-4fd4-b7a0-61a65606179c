<template>
  <view class="course-list-container">
    <z-paging 
      ref="paging" 
      v-model="courseList" 
      @query="queryList"
      :refresher-enabled="true"
      :refresher-threshold="80"
      :refresher-default-style="'black'"
      :refresher-pulling-text="'下拉刷新'"
      :refresher-refreshing-text="'正在刷新...'"
      :refresher-complete-text="'刷新完成'"
    >
      <!-- 自定义导航栏 - 放在top插槽中 -->
      <template #top>
        <view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
          <view class="navbar-content">
            <text class="navbar-title">选择课程</text>
          </view>
        </view>
      </template>

      <!-- 课程列表内容 -->
      <view class="course-list">
        <view 
          v-for="course in courseList" 
          :key="course.courseId"
          class="course-item"
          @click="enterCourse(course)"
        >
          <view class="course-content">
            <!-- 课程图标 -->
            <view class="course-icon">
              <view class="default-icon">
                <text class="icon-text">{{ course.courseName.charAt(0) }}</text>
              </view>
            </view>

            <!-- 课程信息 -->
            <view class="course-info">
              <text class="course-name">{{ course.courseName }}</text>
              <text class="course-desc">{{ course.courseIntroduction || '暂无介绍' }}</text>
            </view>

            <!-- 进入按钮 -->
            <view class="enter-button">
              <wd-button
                type="primary"
                size="small"
                custom-style="border-radius: 4px; font-size: 14px; background: #BD0407; border: none; display: flex; align-items: center; justify-content: center;"
              >
                进入课堂
              </wd-button>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态 -->
      <template #empty>
        <view class="empty-state">
          <view class="empty-icon">📚</view>
          <text class="empty-title">暂无进行中的课程</text>
          <text class="empty-desc">请联系老师开启课程</text>
        </view>
      </template>
    </z-paging>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useUserStore } from '@/stores/user';
import config from '@/utils/config';

// 状态管理
const userStore = useUserStore();
const paging = ref(null);
const courseList = ref([]);

// 导航栏高度计算
const statusBarHeight = ref(0);

// 获取系统信息
onMounted(() => {
  const systemInfo = uni.getSystemInfoSync();
  statusBarHeight.value = systemInfo.statusBarHeight || 44;
});

// 分页查询函数 - z-paging会自动调用
const queryList = async (pageNo, pageSize) => {
  try {
    const response = await new Promise((resolve, reject) => {
      uni.request({
        url: config.BASE_URL + '/student/course/list',
        method: 'GET',
        header: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + userStore.token
        },
        success: (res) => {
          if (res.statusCode === 200) {
            const responseData = typeof res.data === 'string' ? JSON.parse(res.data) : res.data;
            resolve(responseData);
          } else {
            reject(new Error(`HTTP错误 ${res.statusCode}: ${res.data?.msg || ''}`));
          }
        },
        fail: reject
      });
    });

    if (response && response.code === 200 && response.data) {
      // 使用z-paging的complete方法完成加载
      paging.value.complete(response.data);
    } else {
      throw new Error(response?.msg || '获取课程列表失败');
    }
  } catch (error) {
    console.error('加载课程列表失败:', error);
    // 使用z-paging的complete方法处理错误
    paging.value.complete(false);
    uni.showToast({
      title: '加载失败',
      icon: 'none',
      duration: 2000
    });
  }
};

// 进入课程
const enterCourse = (course) => {
  // 跳转到课程首页，传递课程信息
  uni.navigateTo({
    url: `/pages/course/index?courseId=${course.courseId}&courseName=${encodeURIComponent(course.courseName)}`
  });
};
</script>

<style lang="scss" scoped>
.course-list-container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.custom-navbar {
  z-index: 1000;
  background-color: #ffffff;
  border-bottom: 1px solid #ddd;
}

.navbar-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
}

.navbar-title {
  font-size: 18px;
  font-weight: 600;
  color: #333333;
}

.course-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
}

.course-item {
  background-color: #ffffff;
  border-radius: 4px;
  overflow: hidden;
  transition: background-color 0.2s ease;
  border: 1px solid #ddd;
}

.course-item:active {
  background-color: #f5f5f5;
}

.course-content {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 12px;
}

.course-icon {
  width: 50px;
  height: 50px;
  border-radius: 4px;
  overflow: hidden;
  flex-shrink: 0;
  border: 1px solid #ddd;
}

.default-icon {
  width: 100%;
  height: 100%;
  background: #BD0407;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-text {
  font-size: 20px;
  font-weight: bold;
  color: #ffffff;
}

.course-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0; /* 确保flex子项可以收缩 */
}

.course-name {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.course-desc {
  font-size: 13px;
  color: #666666;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.enter-button {
  flex-shrink: 0;
  margin-left: 8px; /* 增加与课程信息的间距 */
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.empty-title {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: #999999;
  text-align: center;
}
</style>
