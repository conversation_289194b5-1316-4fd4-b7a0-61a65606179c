<template>
	<view class="connection-status">
		<!-- 返回按钮 -->
		<wd-icon 
			v-if="showBack" 
			name="arrow-left" 
			size="16px" 
			color="#666666"
			@click="handleBack"
			class="back-btn"
		/>
		
		<view class="status-indicator">
			<!-- 连接正常 - 绿点 -->
			<view v-if="status === 'open'" class="status-dot connected"></view>
			<!-- 连接中 - 黄点带动画 -->
			<view v-else-if="status === 'connecting'" class="status-dot connecting"></view>
			<!-- 连接断开/错误 - 红点 -->
			<view v-else class="status-dot disconnected"></view>
		</view>
		<text class="status-text">{{ statusText }}</text>
		<button 
			v-if="showReconnectButton" 
			@click="handleReconnect" 
			class="reconnect-btn"
			size="mini"
		>
			重连
		</button>
	</view>
</template>

<script setup>
import { computed } from 'vue';

// 定义组件props
const props = defineProps({
	status: {
		type: String,
		default: 'disconnected',
		validator: (value) => ['disconnected', 'connecting', 'open', 'error', 'close'].includes(value)
	},
	reason: {
		type: String,
		default: ''
	},
	showBack: {
		type: Boolean,
		default: false
	}
});

// 定义组件事件
const emit = defineEmits(['reconnect', 'back']);

// 计算状态文本
const statusText = computed(() => {
	switch (props.status) {
		case 'disconnected': return '已断开';
		case 'connecting': return '连接中...';
		case 'open': return '已连接';
		case 'error': return '连接错误' + (props.reason ? `(${props.reason})` : '');
		case 'close': return '已关闭' + (props.reason ? `(${props.reason})` : '');
		default: return '未知状态';
	}
});

// 计算是否显示重连按钮
const showReconnectButton = computed(() => {
	return props.status !== 'open' && props.status !== 'connecting';
});

// 处理重连按钮点击
const handleReconnect = () => {
	emit('reconnect');
};

// 处理返回按钮点击
const handleBack = () => {
	emit('back');
};
</script>

<style scoped>
.connection-status {
	display: flex;
	align-items: center;
	justify-content: flex-start;
	gap: 6px;
	padding: 4px 8px;
	background-color: rgba(255, 255, 255, 0.8);
	border-radius: 12px;
	font-size: 11px;
	color: #666666;
	backdrop-filter: blur(10px);
	-webkit-backdrop-filter: blur(10px);
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.status-indicator {
	display: flex;
	align-items: center;
	justify-content: center;
}

.status-dot {
	width: 8px;
	height: 8px;
	border-radius: 50%;
	flex-shrink: 0;
}

/* 连接正常 - 绿点 */
.status-dot.connected {
	background-color: #52c41a;
	box-shadow: 0 0 3px rgba(82, 196, 26, 0.5);
}

/* 连接断开/错误 - 红点 */
.status-dot.disconnected {
	background-color: #ff4d4f;
	box-shadow: 0 0 3px rgba(255, 77, 79, 0.5);
}

/* 连接中 - 黄点带动画 */
.status-dot.connecting {
	background-color: #faad14;
	animation: pulse 1.5s ease-in-out infinite;
}

/* 连接中的脉冲动画 */
@keyframes pulse {
	0% {
		transform: scale(1);
		opacity: 1;
		box-shadow: 0 0 3px rgba(250, 173, 20, 0.5);
	}
	50% {
		transform: scale(1.2);
		opacity: 0.7;
		box-shadow: 0 0 8px rgba(250, 173, 20, 0.8);
	}
	100% {
		transform: scale(1);
		opacity: 1;
		box-shadow: 0 0 3px rgba(250, 173, 20, 0.5);
	}
}

.status-text {
	font-size: 11px;
	color: #666666;
	white-space: nowrap;
}

.reconnect-btn {
	height: 20px;
	line-height: 20px;
	padding: 0 6px;
	font-size: 10px;
	background-color: #ff9500;
	color: white;
	border: none;
	border-radius: 10px;
	margin: 0;
	cursor: pointer;
	transition: background-color 0.2s ease;
}

.reconnect-btn:hover {
	background-color: #e8860c;
}

.reconnect-btn:active {
	background-color: #d6780a;
}

.back-btn {
	margin-right: 8px;
	padding: 4px;
	cursor: pointer;
	transition: opacity 0.2s ease;
	border-radius: 4px;
}

.back-btn:hover {
	opacity: 0.7;
	background-color: rgba(0, 0, 0, 0.05);
}

.back-btn:active {
	opacity: 0.5;
}
</style> 