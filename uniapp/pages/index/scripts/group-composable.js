/**
 * 群组相关可组合逻辑
 */
import { ref, computed } from 'vue';
import { get } from '@/utils/request.js';
import { useUserStore } from '@/stores/user.js';
import { markGroupAsRead } from '@/utils/websocket.js';

export function useGroups() {
    // 群组相关状态
    const groups = ref([]); // 群组列表
    const currentGroupId = ref(''); // 当前选中的群组 ID
    const currentCourseId = ref(''); // 当前选中群组对应的课程 ID
    const currentSceneId = ref(''); // 当前选中群组对应的场景 ID
    const groupUnreadCounts = ref({}); // 群组未读消息数

    // 当前群组的计算属性
    const currentGroup = computed(() => {
        return groups.value.find(g => g.id === currentGroupId.value) || {};
    });

    // 轮询相关状态
    const pollingInterval = ref(null); // 轮询定时器
    const pollingIntervalTime = ref(30000); // 轮询间隔30秒
    const isPolling = ref(false); // 是否正在轮询中
    const lastGroupUpdateTime = ref(0); // 上次群组更新时间，用于防止频繁提示
    const pollingEnabled = ref(false); // 轮询功能是否启用

    // 获取轮询配置
    const fetchPollingConfig = async () => {
        try {
            const response = await get('/student/chat/config/polling');
            if (response.code === 200) {
                pollingEnabled.value = response.data;
                console.log('[GroupComposable] 轮询配置获取成功:', pollingEnabled.value);
                return pollingEnabled.value;
            } else {
                console.error('[GroupComposable] 获取轮询配置失败:', response);
                pollingEnabled.value = false; // 默认关闭
                return false;
            }
        } catch (error) {
            console.error('[GroupComposable] 获取轮询配置异常:', error);
            pollingEnabled.value = false; // 默认关闭
            return false;
        }
    };

    // 获取课程和群组信息
    const fetchGroupsInfo = async (chatType = null) => {
        const userStore = useUserStore();
        
        // 检查用户是否已登录
        if (!userStore.userInfo?.userId) {
            console.error('[GroupComposable] User not logged in or userId not found');
            uni.showToast({
                title: '请先登录',
                icon: 'none'
            });
            return [];
        }
        
        console.log('[GroupComposable] Current user:', userStore.userInfo, 'chatType:', chatType);

        try {
            let groupResponse;
            
            // 根据chatType调用不同的接口
            if (chatType === 'class') {
                // 调用班级群接口
                console.log('[GroupComposable] Fetching class groups...');
                groupResponse = await get('/student/chat/groups/byType?type=CLASS');
            } else if (chatType === 'group') {
                // 调用课程群接口
                console.log('[GroupComposable] Fetching study groups...');
                groupResponse = await get('/student/chat/groups/byType?type=STUDY_GROUP');
            } else {
                // 没有指定类型，调用原来的接口获取所有群组
                console.log('[GroupComposable] Fetching all groups...');
                groupResponse = await get('/student/chat/currentUser/courseInfo');
            }
            
            // 获取班级信息（用于班级大群，可选功能）
            let classResponse = null;
            try {
                classResponse = await get('/student/user/classInfo');
            } catch (error) {
                console.warn('[GroupComposable] Failed to fetch class info (API may not exist):', error);
                classResponse = null;
            }
            
            let fetchedGroups = [];
            
            // 处理群组聊天室
            if (groupResponse.code === 200 && Array.isArray(groupResponse.data)) {
                const groupChats = groupResponse.data
                    .map(dto => ({
                        // 确保 id 是字符串类型
                        id: dto.groupId ? String(dto.groupId) : (dto.teachingClassId ? String(dto.teachingClassId) : null),
                        name: dto.groupName || dto.teachingClassName || '未知群组',
                        type: 'group', // 标识为群组聊天室
                        isClass: false,
                        isCourse: dto.isCourse || false, // 添加 isCourse 标识
                        courseId: dto.courseId || '', // 提取并存储courseId
                        sceneId: dto.sceneId || '', // 提取并存储sceneId
                        puppetName: dto.puppetName || '', // 添加puppetName
                        puppetIcon: dto.puppetIcon || '', // 添加puppetIcon
                    }))
                    .filter(g => g.id); // 过滤掉没有有效 ID 的群组
                
                fetchedGroups = fetchedGroups.concat(groupChats);
            }
            
            // 处理班级大群
            if (classResponse.code === 200 && classResponse.data) {
                const classInfo = classResponse.data;
                if (classInfo.classCode) {
                    // 使用classCode作为班级大群的ID
                    const classGroup = {
                        id: String(classInfo.classCode),
                        name: `${classInfo.className || '班级'}大群`,
                        type: 'class', // 标识为班级大群
                        isClass: true,
                        isCourse: false,
                        courseId: '',
                        sceneId: '',
                        puppetName: userStore.userInfo?.nickName || userStore.userInfo?.username || '同学',
                        puppetIcon: userStore.userInfo?.avatar || '',
                        classCode: classInfo.classCode,
                        className: classInfo.className
                    };
                    fetchedGroups.unshift(classGroup); // 班级大群放在前面
                }
            }
            
            if (fetchedGroups.length > 0) {

                    groups.value = fetchedGroups;
                    
                    // 缓存群组组长信息到store
                    const groupLeaderInfo = {};
                    if (groupResponse.code === 200 && Array.isArray(groupResponse.data)) {
                        groupResponse.data.forEach(dto => {
                            if (dto.groupId && dto.groupId !== 'public') {
                                groupLeaderInfo[dto.groupId] = dto.isGroupLeader === true || dto.isGroupLeader === 1;
                            }
                        });
                    }
                    userStore.setGroupLeaderInfo(groupLeaderInfo);
                    console.log('[GroupComposable] 缓存群组组长信息:', groupLeaderInfo);

                    return fetchedGroups;
            } else {
                groups.value = [];
                console.log('[GroupComposable] No course/group info found for the user.');
                uni.showToast({
                    title: '未获取到群组信息',
                    icon: 'none'
                });
                return [];
            }
        } catch (error) {
            groups.value = [];
            console.error('[GroupComposable] Error fetching course info:', error);
            uni.showToast({
                title: '获取群组信息异常',
                icon: 'error'
            });
            return [];
        }
    };

    // 切换群组
    const switchGroup = async (groupIdToSwitch, isReconnectRefresh = false, onGroupSwitched) => {
        console.log(`[GroupComposable] Switching to group: ${groupIdToSwitch}`);
        
        // 如果是重连刷新，并且群组未变，则仅获取消息，不重置未读数或发送已读标记
        if (isReconnectRefresh && currentGroupId.value === groupIdToSwitch) {
            // 仅刷新当前群组消息
        } else {
            if (currentGroupId.value === groupIdToSwitch) return;
            if (currentGroupId.value) { // 只有在之前有选定群组时才标记已读
               markGroupAsRead(currentGroupId.value);
               groupUnreadCounts.value[currentGroupId.value] = 0;
            }
        }
        
        // 更新当前群组
        currentGroupId.value = groupIdToSwitch;
        
        // 根据群组ID查找对应的courseId和sceneId
        const targetGroup = groups.value.find(g => g.id === groupIdToSwitch);
        currentCourseId.value = targetGroup ? targetGroup.courseId : '';
        currentSceneId.value = targetGroup ? targetGroup.sceneId : '';
        
        // 切换群组后，立即将新群组的未读数清零 (因为我们将要加载并看到它的消息)
        groupUnreadCounts.value[groupIdToSwitch] = 0;
        
        // 调用回调函数，通知父组件群组已切换
        if (onGroupSwitched) {
            onGroupSwitched(groupIdToSwitch, targetGroup, isReconnectRefresh);
        }
    };

    // 检查群组更新
    const checkGroupUpdates = async (onGroupsUpdated) => {
        if (isPolling.value) return; // 防止重复轮询
        
        const userStore = useUserStore();
        if (!userStore.userInfo?.userId) {
            console.log('[GroupPolling] User not logged in, skip polling');
            return;
        }
        
        isPolling.value = true;
        console.log('[GroupPolling] Checking for group updates...');
        
        try {
            const response = await get('/student/chat/currentUser/courseInfo');
            if (response.code === 200 && Array.isArray(response.data)) {
                const newGroups = response.data
                    .map(dto => ({
                        id: dto.groupId ? String(dto.groupId) : (dto.teachingClassId ? String(dto.teachingClassId) : null),
                        name: dto.groupName || dto.teachingClassName || '未知群组',
                        isCourse: dto.isCourse || false, // 添加 isCourse 标识
                        courseId: dto.courseId || '',
                        sceneId: dto.sceneId || '',
                        puppetName: dto.puppetName || '',
                        puppetIcon: dto.puppetIcon || '',
                    }))
                    .filter(g => g.id);
                
                // 比较新旧群组列表
                const oldGroupIds = new Set(groups.value.map(g => g.id));
                const newGroupIds = new Set(newGroups.map(g => g.id));
                
                // 检查是否有新增的群组
                const addedGroups = newGroups.filter(g => !oldGroupIds.has(g.id));
                
                // 检查是否有移除的群组
                const removedGroupIds = groups.value.filter(g => !newGroupIds.has(g.id)).map(g => g.id);
                
                if (addedGroups.length > 0 || removedGroupIds.length > 0) {
                    console.log('[GroupPolling] Group changes detected:', {
                        added: addedGroups,
                        removed: removedGroupIds
                    });
                    
                    // 更新群组列表
                    groups.value = newGroups;
                    
                    // 更新群组组长信息缓存
                    const groupLeaderInfo = {};
                    response.data.forEach(dto => {
                        if (dto.groupId && dto.groupId !== 'public') {
                            groupLeaderInfo[dto.groupId] = dto.isGroupLeader === true || dto.isGroupLeader === 1;
                        }
                    });
                    userStore.setGroupLeaderInfo(groupLeaderInfo);
                    
                    // 如果有新增群组，显示通知
                    if (addedGroups.length > 0) {
                        const now = Date.now();
                        // 防止频繁提示，至少间隔5秒
                        if (now - lastGroupUpdateTime.value > 5000) {
                            const groupNames = addedGroups.map(g => g.name).join('、');
                            uni.showToast({
                                title: `您已加入新群组：${groupNames}`,
                                icon: 'none',
                                duration: 3000
                            });
                            lastGroupUpdateTime.value = now;
                        }
                    }
                    
                    // 通知父组件群组更新
                    if (onGroupsUpdated) {
                        onGroupsUpdated(addedGroups, removedGroupIds);
                    }
                } else {
                    console.log('[GroupPolling] No group changes detected');
                }
            } else {
                console.warn('[GroupPolling] Invalid response format:', response);
            }
        } catch (error) {
            console.error('[GroupPolling] Error checking group updates:', error);
            // 轮询错误不显示用户提示，避免干扰用户体验
        } finally {
            isPolling.value = false;
        }
    };

    // 启动轮询
    const startPolling = (onGroupsUpdated) => {
        // 检查轮询是否启用
        if (!pollingEnabled.value) {
            console.log('[GroupPolling] 轮询功能已关闭，跳过启动轮询');
            return;
        }

        if (pollingInterval.value) {
            clearInterval(pollingInterval.value);
        }
        
        console.log(`[GroupPolling] Starting group polling with interval: ${pollingIntervalTime.value}ms`);
        pollingInterval.value = setInterval(() => {
            checkGroupUpdates(onGroupsUpdated);
        }, pollingIntervalTime.value);
    };

    // 停止轮询
    const stopPolling = () => {
        if (pollingInterval.value) {
            clearInterval(pollingInterval.value);
            pollingInterval.value = null;
            console.log('[GroupPolling] Stopped group polling');
        }
    };

    return {
        // 状态
        groups,
        currentGroupId,
        currentGroup,
        currentCourseId,
        currentSceneId,
        groupUnreadCounts,
        isPolling,
        lastGroupUpdateTime,
        pollingEnabled,
        fetchPollingConfig,
        checkGroupUpdates,
        startPolling,
        stopPolling,
        pollingIntervalTime,

        // 方法
        fetchGroupsInfo,
        switchGroup
    };
} 