<template>
	<view class="page-container">
		<!-- 状态栏区域 - 和微信胶囊按钮在同一行 -->
		<view class="top-status-bar">
			<ConnectionStatus 
				:status="globalWebSocket.connectionStatus.value"
				:reason="globalWebSocket.connectionReason.value"
				:showBack="true"
				@reconnect="reconnect"
				@back="handleBack"
			/>
			<view class="refresh-button" @click="handleRefreshMessages">
				<text>刷新</text>
			</view>
		</view>
		
		<!-- 群组列表组件 -->
		<view class="group-area">
			<GroupListComponent 
				:groups="groups"
				:currentGroupId="currentGroupId"
				:groupUnreadCounts="groupUnreadCounts"
				@switch-group="handleSwitchGroup"
			/>
		</view>

		<!-- 聊天区域组件 -->
		<ChatAreaComponent 
			:currentMessages="currentMessages"
			:scrollToViewId="scrollToViewId"
			:scrollTop="scrollTop"
			:isLoadingMore="isLoadingMore"
			:loadMoreState="loadMoreState"
			@scroll-to-upper="handleScrollToUpper"
			@scroll="handleScroll"
			@load-more-click="handleLoadMoreClick"
		/>
		
		<!-- 输入区域组件 - 固定在底部 -->
		<view class="input-area-fixed">
			<InputAreaComponent 
				v-model:newMessage="newMessage"
				v-model:inputFocused="inputFocused"
				:isConnected="globalWebSocket.isConnected.value"
				@send="handleSend"
			/>
		</view>
	</view>
	
	<!-- 群组成员列表组件 -->
	<GroupMemberList ref="groupMemberListRef" :currentGroupId="currentGroupId" :isCourse="currentGroup.isCourse" />
	
	<!-- 问卷列表组件 -->
	<SurveyList ref="surveyListRef" :currentSceneId="currentSceneId" :currentGroupId="currentGroupId" :currentCourseId="currentCourseId" />
</template>

<script setup>
import { ref, nextTick, onMounted, watch } from 'vue';
import { onLoad, onUnload, onShow, onHide } from '@dcloudio/uni-app';
import { useUserStore } from '@/stores/user.js';
import { useGlobalWebSocket } from '@/utils/global-websocket.js';

// 引入拆分的可组合逻辑
import { useWebSocket } from './scripts/websocket-composable.js';
import { useGroups } from './scripts/group-composable.js';
import { useMessages } from './scripts/message-composable.js';

// 引入子组件
import GroupListComponent from './components/GroupListComponent.vue';
import ChatAreaComponent from './components/ChatAreaComponent.vue';
import InputAreaComponent from './components/InputAreaComponent.vue';
import GroupMemberList from './components/GroupMemberList.vue';
import SurveyList from './components/SurveyList.vue';
import ConnectionStatus from './components/ConnectionStatus.vue';

// 使用可组合逻辑
const websocket = useWebSocket();
const groupsLogic = useGroups();
const messagesLogic = useMessages();
const globalWebSocket = useGlobalWebSocket();

// 从各个可组合逻辑中解构需要的状态和方法
const { 
    connectionStatus, 
    connectionReason, 
    isConnected, 
    connectWebSocket, 
    reconnect: wsReconnect,
    sendMessage,
    setCurrentGroup,
    disconnect
} = websocket;

const {
    groups,
    currentGroupId,
    currentGroup,
    currentCourseId,
    currentSceneId,
    groupUnreadCounts,
    pollingEnabled,
    fetchPollingConfig,
    fetchGroupsInfo,
    switchGroup,
    startPolling,
    stopPolling
} = groupsLogic;

const {
    newMessage,
    messageInput,
    inputFocused,
    scrollToViewId,
    scrollTop,
    currentMessages,
    isLoadingMore,
    loadMoreState,
    scrollToBottom,
    handleMessageReceive,
    focusInput,
    handleSend: messageHandleSend,
    loadGroupMessages,
    handleScrollToUpper: messageHandleScrollToUpper,
    handleScroll,
    loadMoreMessages,
    handleLoadMoreClick: messageHandleLoadMoreClick,
    resetPagination
} = messagesLogic;

// 群组成员列表组件引用
const groupMemberListRef = ref(null);

// 问卷列表组件引用
const surveyListRef = ref(null);

// 固定群组 ID, 用于 WebSocket 连接参数（当没有其他群组时的默认值）
const groupId = ref('public-chat-room'); 

// 添加页面状态管理
const pageVisible = ref(true); // 标记页面是否可见
const lastCheckTime = ref(Date.now()); // 记录上次检查时间

// 用于标记是否是手动重连触发的连接成功
const isManualReconnecting = ref(false);

// 智能检测是否需要重连WebSocket
const shouldReconnectWebSocket = () => {
    const currentTime = Date.now();
    
    // 1. WebSocket未连接是基本条件
    if (globalWebSocket.isConnected.value) {
        return false;
    }
    
    // 2. 必须有群组信息
    if (!groups.value || groups.value.length === 0) {
        return false;
    }
    
    // 3. 检查是否是从后台恢复
    let isFromBackground = false;
    try {
        const app = getApp();
        if (app && app.globalData) {
            const globalData = app.globalData;
            isFromBackground = globalData.lastBackgroundTime && 
                             globalData.lastForegroundTime && 
                             (currentTime - globalData.lastBackgroundTime > 5000); // 5秒
        }
    } catch (error) {
        console.warn('Error accessing app globalData in shouldReconnectWebSocket:', error);
    }
    
    // 4. 检查页面不可见的时间长度
    const invisibleDuration = currentTime - lastCheckTime.value;
    
    // 5. 如果是明确的后台恢复或者页面不可见时间超过30秒，则需要重连
    return isFromBackground || invisibleDuration > 30000;
};

// WebSocket 收到消息的处理函数（适配器函数）
const onMessageReceived = (message) => {
    handleMessageReceive(message, currentGroupId.value, groupUnreadCounts.value);
};

// 重新连接按钮处理函数
const reconnect = () => {
    console.log('[IndexPage] Manual reconnect initiated.');
    // 1. 设置标志位
    isManualReconnecting.value = true; 
    // 2. 调用全局WebSocket重连方法
    globalWebSocket.reconnect();
};

// 返回按钮处理函数
const handleBack = () => {
    console.log('[IndexPage] Back button clicked');
    uni.navigateBack({
        delta: 1
    });
};

// 刷新消息处理函数
const handleRefreshMessages = () => {
    if (currentGroupId.value) {
        console.log(`[IndexPage] Manual refresh messages for current group: ${currentGroupId.value}`);
        loadGroupMessages(currentGroupId.value);
        uni.showToast({
            title: '消息已刷新',
            icon: 'success',
            duration: 1000
        });
    } else {
        uni.showToast({
            title: '请先选择聊天室',
            icon: 'none',
            duration: 1500
        });
    }
};

// 监听WebSocket连接状态的变化
watch(globalWebSocket.connectionStatus, (newStatus, oldStatus) => {
    console.log(`[IndexPage] WebSocket status changed: ${oldStatus} -> ${newStatus}`);
    // 检查是否是从"未连接"状态变为"已连接"
    if (newStatus === 'open' && oldStatus !== 'open') {
        // 检查是否是手动重连触发的
        if (isManualReconnecting.value) {
            console.log('[IndexPage] WebSocket reconnected successfully after manual trigger.');
            
            // 重新加载当前群组的消息
            if (currentGroupId.value) {
                console.log(`Reloading messages for current group: ${currentGroupId.value}`);
                loadGroupMessages(currentGroupId.value);
            }
            
            // 重置标志位，避免后续自动重连也触发加载
            isManualReconnecting.value = false;
        }
    }
});

// 切换群组的处理函数
const handleSwitchGroup = async (groupIdToSwitch) => {
    await switchGroup(groupIdToSwitch, false, async (groupId, targetGroup, isReconnectRefresh) => {
        // 通知websocket当前查看的群组ID已变更
        setCurrentGroup(groupId);
        
        // 发送JOIN消息通知后端用户当前查看的群组（仅用于业务通知）
        if (globalWebSocket.isConnected.value && !isReconnectRefresh) {
            const userStore = useUserStore();
            const userId = userStore.userInfo?.userId;
            
            if (userId && targetGroup) {
                const joinMessage = {
                    type: 'JOIN',
                    sender: String(userId),
                    nickname: targetGroup.puppetName || userStore.nickname || userStore.username || `用户${userId}`,
                    avatar: targetGroup.puppetIcon || userStore.avatar || '',
                    groupId: groupId,
                    courseId: currentCourseId.value || '',
                    content: `${targetGroup.puppetName || userStore.nickname || userStore.username || `用户${userId}`}查看了群组${targetGroup.name}`
                };
                
                globalWebSocket.sendMessage(joinMessage);
                console.log(`[IndexPage] Sent JOIN message for group: ${targetGroup.name} (${groupId})`);
            }
        }
        
        // 加载群组消息
        await loadGroupMessages(groupId);
    });
};

// 发送消息的处理函数
const handleSend = () => {
    messageHandleSend(currentGroupId.value, currentCourseId.value, groups.value, sendMessage);
};

// 加载更多消息点击处理函数
const handleLoadMoreClick = () => {
    messageHandleLoadMoreClick(currentGroupId.value);
};

// 处理滚动到顶部事件
const handleScrollToUpper = () => {
    console.log('[IndexPage] handleScrollToUpper triggered, calling loadMoreMessages');
    loadMoreMessages(currentGroupId.value);
};

// 获取课程和群组信息并初始化
const fetchAndInitializeChat = async (retryCount = 0, chatType = null) => {
    const userStore = useUserStore();
    
    // 检查用户是否已登录
    if (!userStore.userInfo?.userId) {
        console.error('[IndexPage] User not logged in or userId not found');
        uni.showToast({
            title: '请先登录',
            icon: 'none'
        });
        return;
    }
    
    console.log(`[IndexPage] Attempting to fetch and initialize chat (attempt ${retryCount + 1})`);
    
    // 首先获取轮询配置
    await fetchPollingConfig();
    console.log(`[IndexPage] 轮询配置: ${pollingEnabled.value ? '启用' : '禁用'}`);
    
    // 获取群组信息
    const fetchedGroups = await fetchGroupsInfo(chatType);
    
    if (fetchedGroups && fetchedGroups.length > 0) {
        console.log('[IndexPage] Groups fetched successfully, connecting WebSocket and selecting first group');
        // 先连接WebSocket并加入所有群组
        connectWebSocket(fetchedGroups, onMessageReceived);

        // 新接口已经按类型过滤，直接选择第一个群组
        const targetGroupId = fetchedGroups[0].id;
        console.log('[IndexPage] Selected group:', targetGroupId, 'chatType:', chatType);
        
        await handleSwitchGroup(targetGroupId);
    } else {
        // 没有群组则清空消息列表
        currentMessages.value = [];
        console.log('[IndexPage] No groups available, will rely on polling to detect groups later');
        
        // 如果是网络错误且重试次数未达到限制，则进行重试
        if (retryCount < 2) { // 最多重试2次
            console.log(`[IndexPage] Retrying initialization in 3 seconds (retry ${retryCount + 1}/2)`);
            setTimeout(() => {
                fetchAndInitializeChat(retryCount + 1, chatType);
            }, 3000);
        } else {
            console.log('[IndexPage] Max retry attempts reached, will wait for polling to detect groups');
            uni.showToast({
                title: '网络连接异常，正在后台重试...',
                icon: 'none',
                duration: 2000
            });
        }
    }
};

// 加载已存在的群组和消息（当WebSocket已连接时）
const loadExistingGroupsAndMessages = async (chatType = null) => {
    console.log('[IndexPage] Loading existing groups and messages from global WebSocket');
    
    // 获取群组信息
    const fetchedGroups = await fetchGroupsInfo(chatType);
    
    if (fetchedGroups && fetchedGroups.length > 0) {
        console.log('[IndexPage] Existing groups loaded');
        
        // 新接口已经按类型过滤，直接选择第一个群组
        const targetGroupId = fetchedGroups[0].id;
        console.log('[IndexPage] Selected group:', targetGroupId, 'chatType:', chatType);
        
        await handleSwitchGroup(targetGroupId);
    }
};

// 群组更新的处理函数
const onGroupsUpdated = (addedGroups, removedGroupIds) => {
    console.log('[IndexPage] Groups updated - added:', addedGroups, 'removed:', removedGroupIds);
    
    // 如果当前选中的群组被移除，切换到第一个可用群组
    if (removedGroupIds.includes(currentGroupId.value)) {
        console.log('[IndexPage] Current group was removed, switching to first available group');
        if (groups.value.length > 0) {
            handleSwitchGroup(groups.value[0].id);
        } else {
            currentGroupId.value = '';
            currentMessages.value = [];
        }
    }
    
    // 如果有新增群组
    if (addedGroups.length > 0) {
        console.log('[IndexPage] New groups added, checking if WebSocket reconnection is needed');
        
        // 如果当前没有选中任何群组（比如初始化失败后恢复的情况）
        if (!currentGroupId.value && groups.value.length > 0) {
            console.log('[IndexPage] No current group selected, auto-selecting first group after recovery');
            
            // 如果WebSocket未连接，先建立连接
            if (!globalWebSocket.isConnected.value) {
                console.log('[IndexPage] WebSocket not connected, reconnecting with new groups');
                connectWebSocket(groups.value, onMessageReceived);
                
                // 等待WebSocket连接建立后选择首个群组
                const checkConnection = () => {
                    if (globalWebSocket.isConnected.value) {
                        handleSwitchGroup(groups.value[0].id);
                    } else {
                        // 如果还未连接，继续等待
                        setTimeout(checkConnection, 500);
                    }
                };
                setTimeout(checkConnection, 1000); // 给WebSocket连接一些时间
            } else {
                // WebSocket已连接，直接选择首个群组
                handleSwitchGroup(groups.value[0].id);
            }
        } else {
            // 当前已有选中群组，仅重新连接WebSocket以包含新的群组
            reconnectWebSocketWithNewGroups();
        }
    }
};

// 重新连接WebSocket以包含新群组
const reconnectWebSocketWithNewGroups = () => {
    console.log('[IndexPage] Reconnecting WebSocket with updated groups');
    // 断开当前连接
    disconnect();
    // 稍作延迟后重新连接
    setTimeout(() => {
        connectWebSocket(groups.value, onMessageReceived);
    }, 1000);
};

// 页面加载时执行
onLoad((options) => {
	console.log('Index Page loaded with options:', options, 'checking login status...');
    
    // 检查用户登录状态
    const userStore = useUserStore();
    if (!userStore.isLoggedIn) {
        console.log('User not logged in, redirecting to login...');
        uni.reLaunch({
            url: '/pages/login/index'
        });
        return;
    }
    
    console.log('User logged in, checking global WebSocket status...');
    
    // 保存页面参数，用于后续的聊天室选择
    const chatType = options.chatType; // 'group' 或 'class'
    console.log('[IndexPage] Chat type from route:', chatType);
    
    // 通知全局WebSocket管理器页面导航
    globalWebSocket.navigateToPage('/pages/index/index', onMessageReceived);
    
    // 检查是否需要获取群组信息并初始化
    if (globalWebSocket.needsReinitialization()) {
        console.log('Global WebSocket needs initialization, fetching data...');
        fetchAndInitializeChat(0, chatType); // 获取数据并初始化聊天
    } else {
        console.log('Global WebSocket already connected, loading groups and messages...');
        // WebSocket已经连接，只需要加载群组和消息
        loadExistingGroupsAndMessages(chatType);
    }
    
    // 启动群组轮询 - 仅当配置启用时才启动
    if (pollingEnabled.value) {
        console.log('[IndexPage] 轮询功能已启用，开始启动轮询');
        
        const enhancedOnGroupsUpdated = (addedGroups, removedGroupIds) => {
            onGroupsUpdated(addedGroups, removedGroupIds);
            
            // 如果成功获取到群组且之前没有群组，说明服务器恢复了
            // 调整回正常的轮询频率
            if (addedGroups.length > 0 && !currentGroupId.value) {
                console.log('[IndexPage] Server recovered, adjusting to normal polling frequency');
                stopPolling();
                startPolling(onGroupsUpdated); // 重启为正常频率的轮询
            }
        };
        
        // 如果当前没有群组（说明初始化可能失败），使用更频繁的轮询（10秒一次）
        if (groups.value.length === 0) {
            console.log('[IndexPage] No initial groups, starting frequent polling for server recovery');
            // 设置更频繁的轮询间隔
            groupsLogic.pollingIntervalTime.value = 10000; // 10秒一次
            startPolling(enhancedOnGroupsUpdated);
            
            // 2分钟后如果还是没有群组，恢复正常轮询频率
            setTimeout(() => {
                if (groups.value.length === 0) {
                    console.log('[IndexPage] Still no groups after 2 minutes, reverting to normal polling frequency');
                    stopPolling();
                    groupsLogic.pollingIntervalTime.value = 30000; // 恢复30秒一次
                    startPolling(onGroupsUpdated);
                }
            }, 120000); // 2分钟
        } else {
            // 有群组则使用正常轮询
            startPolling(onGroupsUpdated);
        }
    } else {
        console.log('[IndexPage] 轮询功能已禁用，跳过启动轮询');
    }
    
    nextTick(() => {
        scrollToViewId.value = 'bottom'; // 初始滚动到底部
    });
});

// 页面卸载时执行
onUnload(() => {
	console.log('Index Page unloading...');
    // 停止轮询
    stopPolling();
    // 只清空当前消息，不断开全局WebSocket连接
    // 全局WebSocket连接应该在应用退出时才断开
    currentMessages.value = []; // 清空当前消息
    
    // 通知全局WebSocket管理器页面已卸载
    globalWebSocket.navigateBack();
});

// 页面隐藏时执行（切换到后台或其他页面）
onHide(() => {
    console.log('Index Page hidden');
    pageVisible.value = false;
    lastCheckTime.value = Date.now();
});

// 页面显示时执行（从后台回到前台）
onShow(() => {
    console.log('Index Page shown, checking for background recovery...');
    pageVisible.value = true;
    lastCheckTime.value = Date.now();
    
    // 检查用户登录状态
    const userStore = useUserStore();
    if (!userStore.isLoggedIn) {
        console.log('User not logged in, redirecting to login...');
        uni.reLaunch({
            url: '/pages/login/index'
        });
        return;
    }
    
    // 检查是否是从后台恢复（通过应用级别的状态和时间差判断）
    let isFromBackground = false;
    try {
        const app = getApp();
        if (app && app.globalData) {
            const globalData = app.globalData;
            const currentTime = Date.now();
            
            // 如果应用刚从后台切回前台，且时间间隔大于10秒，认为可能需要重连
            isFromBackground = globalData.lastBackgroundTime && 
                             globalData.lastForegroundTime && 
                             (currentTime - globalData.lastBackgroundTime > 10000); // 10秒
        }
    } catch (error) {
        console.warn('Error accessing app globalData in onShow:', error);
    }
    
    // 使用智能检测函数判断是否需要重连
    if (shouldReconnectWebSocket()) {
        console.log('Smart detection: WebSocket reconnection needed');
        
        // 重新连接WebSocket
        connectWebSocket(groups.value, onMessageReceived);
        
        // 重新加载当前群组的消息（如果有选中的群组）
        if (currentGroupId.value) {
            console.log(`Reloading messages for current group: ${currentGroupId.value}`);
            
            // 等待WebSocket连接建立后重新加载消息
            const reloadMessagesAfterConnection = () => {
                if (globalWebSocket.isConnected.value) {
                    // 重新加载当前群组的消息
                    loadGroupMessages(currentGroupId.value);
                    
                    // 发送JOIN消息通知后端用户重新查看该群组
                    const currentGroup = groups.value.find(g => g.id === currentGroupId.value);
                    if (currentGroup) {
                        const joinMessage = {
                            type: 'JOIN',
                            sender: String(userStore.userInfo?.userId),
                            nickname: currentGroup.puppetName || userStore.nickname || userStore.username || `用户${userStore.userInfo?.userId}`,
                            avatar: currentGroup.puppetIcon || userStore.avatar || '',
                            groupId: currentGroupId.value,
                            courseId: currentCourseId.value || '',
                            content: `${currentGroup.puppetName || userStore.nickname || userStore.username || `用户${userStore.userInfo?.userId}`}重新进入了群组${currentGroup.name}`
                        };
                        
                        globalWebSocket.sendMessage(joinMessage);
                        console.log(`[IndexPage] Sent rejoin message for group: ${currentGroup.name} (${currentGroupId.value})`);
                    }
                } else {
                    // 如果还未连接，继续等待
                    setTimeout(reloadMessagesAfterConnection, 500);
                }
            };
            
            // 给WebSocket连接一些时间，然后重新加载消息
            setTimeout(reloadMessagesAfterConnection, 1000);
        }
        
        // 显示恢复连接的提示
        uni.showToast({
            title: '正在恢复连接...',
            icon: 'loading',
            duration: 2000
        });
    } else if (globalWebSocket.isConnected.value && currentGroupId.value && isFromBackground) {
        // 如果WebSocket已连接但是从后台恢复，也重新加载一下消息以防遗漏
        console.log('WebSocket connected but returning from background, refreshing messages...');
        loadGroupMessages(currentGroupId.value);
        
        uni.showToast({
            title: '刷新消息中...',
            icon: 'loading',
            duration: 1500
        });
    }
    
    // 页面从后台回到前台时，如果轮询启用，立即检查一次群组更新
    // 这样可以更快地发现新的群组分配，而不用等待定时器
    if (pollingEnabled.value) {
        groupsLogic.checkGroupUpdates(onGroupsUpdated);
    }
});
</script>

<style src="./styles/index.scss"></style>
