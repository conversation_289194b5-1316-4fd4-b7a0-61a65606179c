# 聊天室功能更新说明

本次更新实现了从课程首页进入特定聊天室的功能，支持群组讨论和班级大群两种类型。

## 主要变更

### 1. 课程首页按钮修改 (`pages/course/index.vue`)

#### 群组讨论按钮
```javascript
const handleGroupDiscussion = () => {
  // 跳转到群组聊天室
  uni.navigateTo({
    url: '/pages/index/index?chatType=group'
  });
};
```

#### 班级大群按钮
```javascript
const handleClassGroup = () => {
  // 跳转到班级大群
  uni.navigateTo({
    url: '/pages/index/index?chatType=class'
  });
};
```

### 2. 聊天页面路由参数处理 (`pages/index/index.vue`)

#### 参数解析
```javascript
onLoad((options) => {
  const chatType = options.chatType; // 'group' 或 'class'
  
  if (globalWebSocket.needsReinitialization()) {
    fetchAndInitializeChat(0, chatType);
  } else {
    loadExistingGroupsAndMessages(chatType);
  }
});
```

#### 聊天室自动选择逻辑
```javascript
// 根据chatType选择对应的群组
if (chatType === 'class') {
  // 查找班级大群
  const classGroup = fetchedGroups.find(group => group.isClass === true || group.type === 'class');
  targetGroupId = classGroup ? classGroup.id : fetchedGroups[0].id;
} else if (chatType === 'group') {
  // 查找群组聊天室
  const groupChat = fetchedGroups.find(group => group.isClass !== true && group.type !== 'class');
  targetGroupId = groupChat ? groupChat.id : fetchedGroups[0].id;
}
```

### 3. 群组数据结构更新

#### 群组聊天室数据结构
```javascript
{
  id: String(dto.groupId),
  name: dto.groupName || '未知群组',
  type: 'group',           // 标识为群组聊天室
  isClass: false,
  isCourse: dto.isCourse || false,
  courseId: dto.courseId || '',
  sceneId: dto.sceneId || '',
  puppetName: dto.puppetName || '',
  puppetIcon: dto.puppetIcon || ''
}
```

#### 班级大群数据结构
```javascript
{
  id: String(classInfo.classCode),  // 使用classCode作为ID
  name: `${classInfo.className}大群`,
  type: 'class',                    // 标识为班级大群
  isClass: true,
  isCourse: false,
  courseId: '',
  sceneId: '',
  puppetName: userInfo.nickName || '同学',
  puppetIcon: userInfo.avatar || '',
  classCode: classInfo.classCode,
  className: classInfo.className
}
```

### 4. 接口调用更新

#### 原有接口保持
- `/student/chat/currentUser/courseInfo` - 获取群组聊天室信息

#### 新增接口
- `/student/user/classInfo` - 获取班级信息用于班级大群

#### 合并逻辑
```javascript
// 获取群组聊天室
const groupResponse = await get('/student/chat/currentUser/courseInfo');

// 获取班级信息
const classResponse = await get('/student/user/classInfo');

// 合并两种类型的聊天室
let fetchedGroups = [];
fetchedGroups = fetchedGroups.concat(groupChats);  // 群组聊天室
fetchedGroups.unshift(classGroup);                 // 班级大群放在最前面
```

## 功能流程

### 用户操作流程
1. 用户在课程首页 (`pages/course/index.vue`)
2. 点击"群组讨论"按钮 → 跳转到群组聊天室
3. 点击"班级大群"按钮 → 跳转到班级大群
4. 聊天页面根据路由参数自动选择对应的聊天室
5. 用户可以正常聊天和接收实时通知

### 技术实现流程
1. 课程首页按钮添加 `chatType` 参数
2. 聊天页面解析路由参数
3. 根据 `chatType` 调用不同的群组选择逻辑
4. WebSocket连接保持全局管理，无需重新连接
5. 自动切换到目标聊天室并加载历史消息

## 群组类型区分

### 群组聊天室 (chatType='group')
- **数据来源**: `/student/chat/currentUser/courseInfo`
- **标识**: `type: 'group'`, `isClass: false`
- **用途**: 课程相关的小组讨论
- **ID**: 使用 `groupId` 或 `teachingClassId`

### 班级大群 (chatType='class')
- **数据来源**: `/student/user/classInfo`
- **标识**: `type: 'class'`, `isClass: true`
- **用途**: 全班同学的交流群
- **ID**: 使用班级的 `classCode`

## 兼容性说明

### 向前兼容
- 保持原有聊天功能不变
- 现有的群组列表展示逻辑保持兼容
- WebSocket连接和消息处理机制不变

### 默认行为
- 如果没有指定 `chatType` 参数，默认选择第一个可用群组
- 如果找不到指定类型的聊天室，fallback到第一个可用群组
- 保持原有的错误处理和重试机制

## 调试和测试

### 关键日志
```javascript
console.log('[IndexPage] Chat type from route:', chatType);
console.log('[IndexPage] Selected class group:', targetGroupId);
console.log('[IndexPage] Selected group chat:', targetGroupId);
```

### 测试用例
1. 从课程首页点击"群组讨论" → 应该进入群组聊天室
2. 从课程首页点击"班级大群" → 应该进入班级大群
3. 直接访问聊天页面 → 应该选择默认群组
4. 网络异常情况下的错误处理
5. WebSocket连接状态的保持和恢复

### 接口验证
- 确保 `/student/user/classInfo` 接口返回正确的班级信息
- 验证 `classCode` 字段存在且唯一
- 确保班级名称 `className` 正确显示

## 注意事项

1. **班级大群ID**: 使用 `classCode` 作为群组ID，确保唯一性
2. **群组顺序**: 班级大群始终显示在群组列表的最前面
3. **用户信息**: 班级大群中使用用户的真实昵称和头像
4. **错误处理**: 如果获取班级信息失败，不影响群组聊天室的正常功能
5. **WebSocket**: 两种类型的聊天室共享同一个WebSocket连接