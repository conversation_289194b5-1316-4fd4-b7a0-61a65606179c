# WebSocket连接迁移说明

本次修改将WebSocket连接逻辑从 `pages/index/index.vue` 迁移到 `pages/course/index.vue`，并实现了全局WebSocket连接管理，确保页面跳转时保持连接状态。

## 主要变更

### 1. 新增全局WebSocket管理器
- **文件**: `utils/global-websocket.js`
- **功能**: 提供应用级别的WebSocket连接管理
- **特性**:
  - 全局连接状态管理
  - 页面间连接保持
  - 自动重连机制
  - 应用级生命周期处理

### 2. 修改课程首页
- **文件**: `pages/course/index.vue`
- **变更**: 
  - 添加WebSocket连接初始化逻辑
  - 在页面加载时建立WebSocket连接
  - 实现实时通知处理
  - 群组讨论和班级大群按钮现在检查连接状态

### 3. 修改聊天页面
- **文件**: `pages/index/index.vue`
- **变更**:
  - 使用全局WebSocket管理器而非自建连接
  - 保留现有UI和功能逻辑
  - 页面卸载时不断开连接，保持全局连接

### 4. 应用生命周期管理
- **文件**: `App.vue`
- **变更**:
  - 添加应用退出时的WebSocket断开逻辑
  - 确保应用完全退出时清理连接资源

## 使用流程

### 用户流程
1. 用户进入 `pages/course/index.vue` (课程首页)
2. 页面自动初始化WebSocket连接
3. 连接建立后，用户可以点击"群组讨论"或"班级大群"
4. 跳转到 `pages/index/index.vue` 时，WebSocket连接保持
5. 在聊天页面可以正常收发消息和实时通知
6. 返回课程首页或跳转到其他页面时，连接继续保持
7. 只有应用完全退出时才断开连接

### 开发者集成
如果需要在其他页面使用WebSocket功能：

```javascript
import { useGlobalWebSocket } from '@/utils/global-websocket.js';

export default {
  setup() {
    const globalWebSocket = useGlobalWebSocket();
    
    // 检查连接状态
    if (globalWebSocket.isConnected.value) {
      // 发送消息
      globalWebSocket.sendMessage({
        type: 'CHAT',
        content: 'Hello',
        groupId: 'group-id'
      });
    }
    
    // 页面导航
    globalWebSocket.navigateToPage('/pages/new-page', messageHandler);
  }
}
```

## API接口

### 全局WebSocket管理器方法

```javascript
const globalWebSocket = useGlobalWebSocket();

// 初始化连接
await globalWebSocket.initialize(groups, onMessageReceive, pagePath);

// 页面导航
globalWebSocket.navigateToPage(newPagePath, newMessageHandler);

// 发送消息
globalWebSocket.sendMessage(messageObject);

// 设置当前群组
globalWebSocket.setCurrentGroup(groupId);

// 重新连接
globalWebSocket.reconnect();

// 断开连接（仅应用退出时）
globalWebSocket.disconnect();

// 获取连接状态
const status = globalWebSocket.getStatus();
```

### 状态属性

```javascript
// 连接状态（reactive）
globalWebSocket.isConnected.value
globalWebSocket.connectionStatus.value
globalWebSocket.connectionReason.value
globalWebSocket.isConnecting.value
```

## 配置说明

### WebSocket连接参数
在 `pages/course/index.vue` 的 `fetchGroupsForWebSocket()` 方法中配置群组获取接口：

```javascript
url: config.BASE_URL + '/student/course/groups'
```

### 实时通知处理
在 `pages/course/index.vue` 的 `handleWebSocketMessage()` 方法中自定义通知处理逻辑。

## 注意事项

1. **页面跳转**: 使用 `globalWebSocket.navigateToPage()` 通知连接管理器页面变化
2. **消息处理**: 每个页面可以提供自己的消息处理回调函数
3. **连接保持**: 不要在页面卸载时调用 `disconnect()`，这会断开全局连接
4. **错误处理**: 全局管理器会自动处理重连，页面只需处理业务逻辑
5. **性能考虑**: 连接在应用级别维护，避免频繁建立/断开连接的开销

## 兼容性

- 保持原有聊天功能不变
- 现有的群组、消息等API保持兼容
- UI组件和交互逻辑无变化
- 支持原有的轮询机制作为备用方案

## 调试指南

### 查看连接状态
```javascript
console.log('WebSocket状态:', globalWebSocket.getStatus());
```

### 查看连接日志
在控制台中搜索以下关键词：
- `[GlobalWebSocket]` - 全局管理器日志
- `[Course Page]` - 课程页面日志
- `[IndexPage]` - 聊天页面日志

### 常见问题排查
1. 连接失败：检查网络状态和服务器地址
2. 消息丢失：确认页面导航时调用了 `navigateToPage()`
3. 重复连接：确认没有在多个地方调用 `initialize()`
4. 连接断开：检查是否意外调用了 `disconnect()`