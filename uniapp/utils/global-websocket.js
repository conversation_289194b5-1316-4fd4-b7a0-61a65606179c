/**
 * 全局 WebSocket 管理器
 * 负责在应用级别管理WebSocket连接，确保页面跳转时保持连接
 */
import { ref, computed } from 'vue';
import { connect as wsConnect, sendMessage as wsSendMessage, disconnect as wsDisconnect, setCurrentGroupId } from './websocket.js';
import { useUserStore } from '@/stores/user.js';

// 全局状态管理
let globalConnectionStatus = ref('disconnected');
let globalConnectionReason = ref('');
let globalIsConnecting = ref(false);
let globalOnMessageReceive = null;
let globalGroups = ref([]);
let isGloballyInitialized = false;

// 计算属性
const isGloballyConnected = computed(() => globalConnectionStatus.value === 'open');

// WebSocket状态变化处理
const handleGlobalStatusChange = (status, reason = '') => {
    console.log(`[GlobalWebSocket] Status Change: ${status}`, reason);
    globalIsConnecting.value = (status === 'connecting');
    globalConnectionStatus.value = status;
    globalConnectionReason.value = typeof reason === 'object' ? (reason.reason || JSON.stringify(reason)) : reason;

    if (status === 'open') {
        globalConnectionReason.value = '';
        console.log('[GlobalWebSocket] Connection established globally');
    } else if (status === 'error' || status === 'close') {
        console.log('[GlobalWebSocket] Connection error or closed');
    }
};

// 全局WebSocket管理器
class GlobalWebSocketManager {
    constructor() {
        this.initialized = false;
        this.currentPage = '';
        this.pageStack = [];
    }

    /**
     * 初始化全局WebSocket连接
     * @param {Array} groups 群组列表
     * @param {Function} onMessageReceive 消息接收回调
     * @param {String} currentPagePath 当前页面路径
     */
    async initializeGlobalConnection(groups, onMessageReceive, currentPagePath = '') {
        if (isGloballyInitialized && isGloballyConnected.value) {
            console.log('[GlobalWebSocket] Already initialized and connected');
            return;
        }

        const userStore = useUserStore();
        if (!userStore.userInfo?.userId) {
            console.warn('[GlobalWebSocket] User not logged in. Cannot initialize WebSocket');
            return;
        }

        if (!groups || groups.length === 0) {
            console.warn('[GlobalWebSocket] No groups available for WebSocket connection');
            return;
        }

        console.log('[GlobalWebSocket] Initializing global WebSocket connection...');
        
        globalGroups.value = groups;
        globalOnMessageReceive = onMessageReceive;
        this.currentPage = currentPagePath;
        isGloballyInitialized = true;

        // 准备群组信息
        const groupsInfo = groups.map(group => ({
            id: group.id,
            name: group.name,
            courseId: group.courseId || '',
            puppetName: group.puppetName || '',
            puppetIcon: group.puppetIcon || ''
        }));

        // 建立WebSocket连接
        wsConnect({
            username: userStore.username || userStore.userInfo.username || `用户${userStore.userInfo.userId}`,
            groups: groupsInfo,
            onStatusChange: handleGlobalStatusChange,
            onMessageReceive: onMessageReceive
        });
    }

    /**
     * 页面导航时调用，更新当前页面但保持连接
     * @param {String} newPagePath 新页面路径
     * @param {Function} newOnMessageReceive 新的消息接收回调（可选）
     */
    navigateToPage(newPagePath, newOnMessageReceive = null) {
        console.log(`[GlobalWebSocket] Navigating from ${this.currentPage} to ${newPagePath}`);
        
        // 更新页面栈
        if (this.currentPage) {
            this.pageStack.push(this.currentPage);
        }
        this.currentPage = newPagePath;

        // 如果提供了新的消息回调，更新它
        if (newOnMessageReceive) {
            globalOnMessageReceive = newOnMessageReceive;
            console.log('[GlobalWebSocket] Updated message receive callback for new page');
            
            // 重新建立WebSocket连接以更新消息回调
            if (isGloballyConnected.value && globalGroups.value.length > 0) {
                console.log('[GlobalWebSocket] Reconnecting to update message callback');
                this.reconnect();
            }
        }

        // WebSocket连接保持不变，只是更新页面上下文
        if (isGloballyConnected.value) {
            console.log('[GlobalWebSocket] WebSocket connection maintained during navigation');
        }
    }

    /**
     * 页面返回时调用
     */
    navigateBack() {
        if (this.pageStack.length > 0) {
            const previousPage = this.pageStack.pop();
            console.log(`[GlobalWebSocket] Navigating back from ${this.currentPage} to ${previousPage}`);
            this.currentPage = previousPage;
        }
    }

    /**
     * 发送消息
     * @param {Object} message 消息对象
     */
    sendMessage(message) {
        if (!isGloballyConnected.value) {
            console.warn('[GlobalWebSocket] Cannot send message, not connected');
            return;
        }
        wsSendMessage(message);
    }

    /**
     * 设置当前群组
     * @param {String} groupId 群组ID
     */
    setCurrentGroup(groupId) {
        setCurrentGroupId(groupId);
    }

    /**
     * 重新连接
     */
    reconnect() {
        if (!isGloballyInitialized) {
            console.warn('[GlobalWebSocket] Cannot reconnect, not initialized');
            return;
        }

        console.log('[GlobalWebSocket] Reconnecting...');
        wsDisconnect();
        
        setTimeout(() => {
            this.initializeGlobalConnection(globalGroups.value, globalOnMessageReceive, this.currentPage);
        }, 1000);
    }

    /**
     * 断开连接（应用退出时调用）
     */
    disconnect() {
        console.log('[GlobalWebSocket] Disconnecting global WebSocket...');
        wsDisconnect();
        globalConnectionStatus.value = 'disconnected';
        globalIsConnecting.value = false;
        isGloballyInitialized = false;
        globalOnMessageReceive = null;
        globalGroups.value = [];
        this.currentPage = '';
        this.pageStack = [];
    }

    /**
     * 获取连接状态
     */
    getConnectionStatus() {
        return {
            status: globalConnectionStatus.value,
            reason: globalConnectionReason.value,
            isConnecting: globalIsConnecting.value,
            isConnected: isGloballyConnected.value,
            isInitialized: isGloballyInitialized
        };
    }

    /**
     * 检查是否需要重新初始化
     */
    needsReinitialization() {
        return !isGloballyInitialized || !isGloballyConnected.value;
    }
}

// 创建全局实例
const globalWebSocketManager = new GlobalWebSocketManager();

// 应用级别的生命周期处理
// 监听应用进入后台
const handleAppHide = () => {
    console.log('[GlobalWebSocket] App is going to background');
    // WebSocket连接保持，不断开
};

// 监听应用回到前台
const handleAppShow = () => {
    console.log('[GlobalWebSocket] App is coming to foreground');
    // 检查连接状态，如果断开则尝试重连
    if (isGloballyInitialized && !isGloballyConnected.value) {
        console.log('[GlobalWebSocket] App resumed, reconnecting WebSocket...');
        globalWebSocketManager.reconnect();
    }
};

// 设置应用级生命周期监听器
try {
    const app = getApp();
    if (app) {
        // 保存原有的生命周期方法
        const originalOnHide = app.onHide;
        const originalOnShow = app.onShow;
        
        // 扩展应用生命周期
        app.onHide = function() {
            handleAppHide();
            if (originalOnHide) originalOnHide.call(this);
        };
        
        app.onShow = function() {
            handleAppShow();
            if (originalOnShow) originalOnShow.call(this);
        };
    }
} catch (error) {
    console.warn('[GlobalWebSocket] Failed to setup app lifecycle listeners:', error);
}

// 导出全局WebSocket管理器和相关状态
export {
    globalWebSocketManager,
    globalConnectionStatus,
    globalConnectionReason,
    globalIsConnecting,
    isGloballyConnected
};

// 为了兼容现有代码，也导出一个可组合函数
export function useGlobalWebSocket() {
    return {
        // 状态
        connectionStatus: globalConnectionStatus,
        connectionReason: globalConnectionReason,
        isConnecting: globalIsConnecting,
        isConnected: isGloballyConnected,
        
        // 方法
        initialize: (groups, onMessageReceive, pagePath) => 
            globalWebSocketManager.initializeGlobalConnection(groups, onMessageReceive, pagePath),
        navigateToPage: (pagePath, onMessageReceive) => 
            globalWebSocketManager.navigateToPage(pagePath, onMessageReceive),
        navigateBack: () => globalWebSocketManager.navigateBack(),
        sendMessage: (message) => globalWebSocketManager.sendMessage(message),
        setCurrentGroup: (groupId) => globalWebSocketManager.setCurrentGroup(groupId),
        reconnect: () => globalWebSocketManager.reconnect(),
        disconnect: () => globalWebSocketManager.disconnect(),
        getStatus: () => globalWebSocketManager.getConnectionStatus(),
        needsReinitialization: () => globalWebSocketManager.needsReinitialization()
    };
}