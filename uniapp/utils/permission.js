import { useUserStore } from '@/stores/user'

// 白名单路由列表
export const whiteList = [
  '/pages/login/index',      // 登录页面
  '/pages/course-list/index' // 课程列表页面
]

// 检查用户是否已登录
export const checkLogin = () => {
  // 使用 Pinia store 检查登录状态
  const userStore = useUserStore()
  return userStore.isLoggedIn
}

// 处理未登录状态
export const handleNotLogin = () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const currentRoute = currentPage ? `/${currentPage.route}` : ''
  
  // 如果当前页面不在白名单中，则跳转到登录页
  if (!whiteList.includes(currentRoute)) {
    uni.reLaunch({ url: '/pages/login/index' })
  }
}

// 路由拦截器
const permissionInterceptor = {
  // 页面跳转前触发
  before: (options) => {
    const url = options.url.split('?')[0] // 获取路由路径，去除参数
    
    // 如果在白名单中，直接放行
    if (whiteList.includes(url)) {
      return true
    }
    
    // 检查是否已登录
    if (checkLogin()) {
      return true
    }
    
    // 未登录，重定向到登录页
    handleNotLogin()
    return false
  }
}

// 注册路由拦截器
uni.addInterceptor('navigateTo', permissionInterceptor)
uni.addInterceptor('redirectTo', permissionInterceptor)
uni.addInterceptor('reLaunch', permissionInterceptor)
uni.addInterceptor('switchTab', permissionInterceptor)

export default permissionInterceptor 