<template>
  <div class="course-stages-page">
    <!-- 头部栏组件 -->
    <ScreenHeader :showCourseInfo="true"/>
    
    <!-- 课堂计时器 -->
    <div v-if="courseStarted && !courseEnded" class="class-timer">
      <div class="timer-content">
        <div class="current-time">当前时间：{{ currentTime }}</div>
        <div class="elapsed-time">课堂时长：{{ elapsedTime }}</div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">

      <!-- 课程阶段步骤展示 -->
      <div class="stages-container">

        <!-- 课程信息展示区域 -->
        <div class="course-info-display" v-if="courseInfo">
          <!-- 课程标题 -->
          <div class="course-title-section">
            <img src="@/assets/images/screen/entry-title-left.png" alt="left" class="title-image left" />
            <h2 class="course-title-text">{{ courseInfo.courseName }}</h2>
            <img src="@/assets/images/screen/entry-title-right.png" alt="right" class="title-image right" />
          </div>
          
          <!-- 课程代码和开始上课按钮 -->
          <div class="course-actions-section">
            <div class="course-action-capsule">
              <div class="course-code-part">{{ route.query.code }}</div>
              <button 
                v-if="!courseStarted && !courseEnded" 
                class="action-btn start-action" 
                @click="startClass">开始上课</button>
              <button 
                v-else-if="courseStarted && !courseEnded" 
                class="action-btn end-action" 
                @click="endClassroom">结束课堂</button>
              <div 
                v-else 
                class="action-btn ended-action">课堂已结束</div>
            </div>
          </div>
        </div>

        <!-- 自定义阶段时间线 -->
        <div class="custom-stages-timeline" v-if="stageList.length > 0">
          <!-- 阶段步骤条 -->
          <div class="stages-steps">
            <div 
              v-for="(stage, index) in stageList" 
              :key="stage.courseStageId"
              class="stage-step-wrapper"
              :class="{ 
                'active': stage.isCompleted === 1,
                'disabled': (!courseStarted && stage.isCompleted === 0) || courseEnded
              }"
              @click="handleStageClick(stage, index)">
              
              <!-- 阶段圆圈和连接线 -->
              <div class="stage-step">
                <div class="stage-circle" :class="{ 'active': stage.isCompleted === 1 }">
                  {{ index + 1 }}
                </div>
                <div v-if="index < stageList.length - 1" class="stage-line" :class="{ 'active': stage.isCompleted === 1 && stageList[index + 1] && stageList[index + 1].isCompleted === 1 }"></div>
              </div>
              
              <!-- 阶段标题 -->
              <div class="stage-title" :class="{ 'active': stage.isCompleted === 1 }">{{ stage.courseStageTitle }}</div>
            </div>
          </div>

          <!-- 所有阶段内容卡片 -->
          <div class="stages-content">
            <div 
              v-for="(stage, index) in stageList" 
              :key="stage.courseStageId"
              class="stage-content-card"
              :class="{ 
                'active': stage.isCompleted === 1,
                'selected': index === selectedStageIndex 
              }">
              
              <!-- 阶段标题 -->
              <div class="stage-card-header" :class="stage.isCompleted === 1 ? 'header-active' : 'header-pending'">
                <div class="stage-title-text">{{ stage.courseStageTitle }}</div>
              </div>
              
              <!-- 阶段内容描述 -->
              <div class="stage-card-content" @click="showFullContent(stage.courseStageText || '暂无阶段内容描述')">
                <p v-if="stage.courseStageText" class="stage-text-content">{{ stage.courseStageText }}</p>
                <p v-else class="no-content">暂无阶段内容描述</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 无阶段提示 -->
        <div v-if="stageList.length === 0 && !loading" class="no-stages">
          <i class="el-icon-warning-outline"></i>
          <h3>暂无课程阶段</h3>
          <p>该课程还未设置阶段信息</p>
        </div>
      </div>
    </div>

    <!-- 悬浮按钮 -->
    <ScreenFab @fab-click="handleFabClick" />
    
    <!-- 内容详情弹窗 -->
    <el-dialog 
      v-model="contentDialogVisible" 
      title="阶段内容详情" 
      width="600px"
      :before-close="closeContentDialog">
      <div class="stage-content-detail">
        <p>{{ selectedStageContent }}</p>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeContentDialog">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="CourseStages">
import { ref, reactive, onMounted, onBeforeUnmount, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
import { getCourseStageListByCourseCode, getCourseSimpleInfo, updateCourseStageStatus, updateCourseState, endClass } from '@/api/screen'
import ScreenHeader from '@/components/ScreenHeader/index.vue'
import ScreenFab from '@/components/ScreenFab/index.vue'

// 路由实例
const route = useRoute()

// 响应式数据
const loading = ref(false)
const stageList = ref([])
const selectedStageIndex = ref(0)
const courseInfo = ref(null)
const courseStarted = ref(false) // 课程是否已开始
const courseEnded = ref(false) // 课程是否已结束
const contentDialogVisible = ref(false) // 内容弹窗显示状态
const selectedStageContent = ref('') // 选中的阶段内容
const currentTime = ref('') // 当前时间
const elapsedTime = ref('00:00:00') // 课堂时长
const startTime = ref(null) // 课堂开始时间
let timerInterval = null // 计时器

// 当前选中的阶段
const selectedStage = computed(() => {
  return stageList.value[selectedStageIndex.value] || null
})

// 当前激活的步骤（用于步骤条显示）
const currentActiveStage = computed(() => {
  // 找到最后一个已完成阶段的索引，如果都未完成则为-1
  let lastCompletedIndex = -1
  for (let i = 0; i < stageList.value.length; i++) {
    if (stageList.value[i].isCompleted === 1) {
      lastCompletedIndex = i
    }
  }
  return lastCompletedIndex
})

// 获取课程阶段数据
const fetchCourseStages = async () => {
  const courseCode = route.query.code
  if (!courseCode) {
    ElMessage.error('缺少课程代码参数')
    return
  }

  loading.value = true
  try {
    // 并行请求课程信息和阶段列表
    const [stageResponse, courseResponse] = await Promise.all([
      getCourseStageListByCourseCode(courseCode),
      getCourseSimpleInfo(courseCode)
    ])
    
    if (stageResponse.code === 200) {
      stageList.value = stageResponse.data || []
      // 默认选中第一个阶段
      if (stageList.value.length > 0) {
        selectedStageIndex.value = 0
      }
    } else {
      ElMessage.error(stageResponse.msg || '获取课程阶段失败')
    }
    
    if (courseResponse.code === 200) {
      courseInfo.value = courseResponse.data
      // 检查课程状态
      const courseState = courseResponse.data?.courseState || courseResponse.data?.status
      courseStarted.value = courseState === '1' || courseState === 1
      courseEnded.value = courseState === '2' || courseState === 2
      
      // 如果课程已经开始且未结束，启动计时器
      if (courseStarted.value && !courseEnded.value) {
        // 使用课程开始时间作为计时起点，如果没有则使用当前时间
        const courseStartTime = courseResponse.data?.startTime
        if (courseStartTime) {
          startTime.value = new Date(courseStartTime)
        } else {
          startTime.value = new Date()
        }
        startTimer()
      }
    }
    
  } catch (error) {
    console.error('获取课程阶段数据失败:', error)
    ElMessage.error('获取课程阶段数据失败')
  } finally {
    loading.value = false
  }
}

// 处理阶段点击事件
const handleStageClick = async (stage, index) => {
  // 检查课程状态，未开始或已结束时不允许点击
  if (courseEnded.value) {
    ElMessage.warning('课堂已结束，不能再修改阶段状态')
    return
  }
  if (!courseStarted.value && stage.isCompleted === 0) {
    ElMessage.warning('请先开始上课后再操作阶段')
    return
  }
  
  console.log('点击阶段:', stage.courseStageTitle, '当前状态:', stage.isCompleted)
  
  // 如果已经完成，则设置为未完成；如果未完成，则设置为完成
  const newStatus = stage.isCompleted === 1 ? 0 : 1
  const statusText = newStatus === 1 ? '完成' : '未完成'
  
  try {
    // 直接调用更新接口
    const response = await updateCourseStageStatus(stage.courseStageId, newStatus)
    
    if (response.code === 200) {
      ElMessage.success(`阶段"${stage.courseStageTitle}"已设置为${statusText}状态`)
      
      // 更新本地数据
      stageList.value[index].isCompleted = newStatus
      
      // 如果设置为完成，需要将之前的阶段也设置为完成
      if (newStatus === 1) {
        for (let i = 0; i < index; i++) {
          if (stageList.value[i].isCompleted === 0) {
            const prevResponse = await updateCourseStageStatus(stageList.value[i].courseStageId, 1)
            if (prevResponse.code === 200) {
              stageList.value[i].isCompleted = 1
            }
          }
        }
      }
      
      // 如果设置为未完成，需要将之后的阶段也设置为未完成
      if (newStatus === 0) {
        for (let i = index + 1; i < stageList.value.length; i++) {
          if (stageList.value[i].isCompleted === 1) {
            const nextResponse = await updateCourseStageStatus(stageList.value[i].courseStageId, 0)
            if (nextResponse.code === 200) {
              stageList.value[i].isCompleted = 0
            }
          }
        }
      }
      
    } else {
      ElMessage.error(response.msg || '更新阶段状态失败')
    }
    
  } catch (error) {
    console.error('更新阶段状态失败:', error)
    ElMessage.error('更新阶段状态失败')
  }
}

// 选择阶段（保留用于其他功能，但不再用于控制active状态）
const selectStage = (index) => {
  selectedStageIndex.value = index
}

// 获取阶段描述
const getStageDescription = (stage) => {
  if (stage.courseStageText && stage.courseStageText.length > 50) {
    return stage.courseStageText.substring(0, 50) + '...'
  }
  return stage.courseStageText || '点击查看详情'
}

// 获取阶段状态类型
const getStageStatusType = (status) => {
  switch (status) {
    case 'completed': return 'success'
    case 'active': return 'warning'
    case 'pending': return 'info'
    default: return 'info'
  }
}

// 获取阶段状态样式类（根据完成状态）
const getStageStatusClass = (stage) => {
  return stage.isCompleted === 1 ? 'status-active' : 'status-pending'
}

// 获取阶段标题头部样式类（根据完成状态）
const getStageHeaderClass = (stage) => {
  return stage.isCompleted === 1 ? 'header-active' : 'header-pending'
}

// 获取阶段状态文本
const getStageStatusText = (status) => {
  switch (status) {
    case 'completed': return '已完成'
    case 'active': return '进行中'
    case 'pending': return '待开始'
    default: return '未知'
  }
}

// 格式化时间
const formatTime = (time) => {
  if (!time) return ''
  const date = new Date(time)
  return date.toLocaleString('zh-CN')
}

// 查看附件
const viewAttachment = (annex) => {
  if (annex.annexPath) {
    window.open(annex.annexPath, '_blank')
  } else {
    ElMessage.warning('附件路径无效')
  }
}

// 处理悬浮按钮点击
const handleFabClick = (item) => {
  console.log('悬浮按钮点击:', item)
}

// 开始上课
const startClass = async () => {
  console.log('开始上课')
  
  const courseCode = route.query.code
  if (!courseCode) {
    ElMessage.error('缺少课程代码参数')
    return
  }
  
  try {
    // 调用API更新课程状态为进行中
    const response = await updateCourseState(courseCode, '1')
    
    if (response.code === 200) {
      ElMessage.success('课程已开始!')
      courseStarted.value = true // 更新课程状态
      startTime.value = new Date() // 设置开始时间为当前时间
      startTimer() // 开始计时器
      
      // 如果有阶段，自动设置第一个阶段为进行中
      if (stageList.value.length > 0) {
        const firstStage = stageList.value[0]
        if (firstStage.isCompleted === 0) {
          try {
            const stageResponse = await updateCourseStageStatus(firstStage.courseStageId, 1)
            if (stageResponse.code === 200) {
              // 更新本地数据
              stageList.value[0].isCompleted = 1
              ElMessage.success(`第一阶段"${firstStage.courseStageTitle}"已自动开始`)
            }
          } catch (stageError) {
            console.error('更新第一阶段状态失败:', stageError)
            ElMessage.warning('课程已开始，但第一阶段状态更新失败')
          }
        }
      }
      
    } else {
      ElMessage.error(response.msg || '开始上课失败')
    }
  } catch (error) {
    console.error('开始上课失败:', error)
    ElMessage.error('开始上课失败')
  }
}

// 显示完整内容
const showFullContent = (content) => {
  selectedStageContent.value = content
  contentDialogVisible.value = true
}

// 关闭内容弹窗
const closeContentDialog = () => {
  contentDialogVisible.value = false
  selectedStageContent.value = ''
}

// 格式化课堂时长为 HH:MM:SS
const formatDuration = (hours, minutes, seconds) => {
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}

// 更新当前时间
const updateCurrentTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-CN', { hour12: false })
}

// 更新课堂时长
const updateElapsedTime = () => {
  if (startTime.value) {
    const now = new Date()
    const elapsed = Math.floor((now - startTime.value) / 1000) // 秒数
    const hours = Math.floor(elapsed / 3600)
    const minutes = Math.floor((elapsed % 3600) / 60)
    const seconds = elapsed % 60
    elapsedTime.value = formatDuration(hours, minutes, seconds)
  }
}

// 开始计时器
const startTimer = () => {
  // 如果没有设置开始时间，则使用当前时间
  if (!startTime.value) {
    startTime.value = new Date()
  }
  
  updateCurrentTime()
  updateElapsedTime()
  
  timerInterval = setInterval(() => {
    updateCurrentTime()
    updateElapsedTime()
  }, 1000)
}

// 停止计时器
const stopTimer = () => {
  if (timerInterval) {
    clearInterval(timerInterval)
    timerInterval = null
  }
}

// 结束课堂
const endClassroom = async () => {
  console.log('结束课堂')
  
  const courseCode = route.query.code
  if (!courseCode) {
    ElMessage.error('缺少课程代码参数')
    return
  }
  
  try {
    // 调用API结束课堂
    const response = await endClass(courseCode)
    
    if (response.code === 200) {
      ElMessage.success('课堂已结束!')
      courseStarted.value = false
      courseEnded.value = true
      stopTimer() // 停止计时器
    } else {
      ElMessage.error(response.msg || '结束课堂失败')
    }
  } catch (error) {
    console.error('结束课堂失败:', error)
    ElMessage.error('结束课堂失败')
  }
}

// 生命周期
onMounted(() => {
  fetchCourseStages()
})

onBeforeUnmount(() => {
  stopTimer() // 清理计时器
})
</script>

<style scoped>
.course-stages-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: url('@/assets/images/screen/entry-background.png') no-repeat 100% 100%;
  color: #333;
}

/* 课堂计时器 */
.class-timer {
  position: fixed;
  top: 80px;
  left: 20px;
  z-index: 50;
}

.timer-content {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 12px 20px;
  border: 1px solid rgba(189, 4, 7, 0.2);
}

.current-time {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.elapsed-time {
  font-size: 16px;
  font-weight: bold;
  color: #BD0407;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  padding: 20px 40px;
  overflow-y: auto;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

/* 课程信息展示 */
.course-info-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(8px);
}

.course-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.course-title {
  display: flex;
  align-items: center;
  gap: 16px;
}

.course-title h1 {
  margin: 0;
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
}

.course-code {
  background: linear-gradient(135deg, #BD0407, #ff6b6b);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.course-meta {
  display: flex;
  gap: 24px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
}

/* 阶段容器 */
.stages-container {
  margin-top: 36px;
  border-radius: 16px;
  padding: 32px;
  backdrop-filter: blur(8px);
}

.section-title {
  text-align: center;
  margin-bottom: 40px;
}

.section-title h2 {
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 600;
  background: linear-gradient(135deg, #BD0407, #ff6b6b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-title p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

/* 课程信息展示区域 */
.course-info-display {
  margin-bottom: 60px;
}

/* 课程标题区域 */
.course-title-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 24px;
  margin-bottom: 24px;
}

.title-image {
  width: 120px;
  height: auto;
}

.title-image.left {
  margin-right: 8px;
}

.title-image.right {
  margin-left: 8px;
}

.course-title-text {
  font-size: 36px;
  font-weight: 700;
  color: #BD0407;
  text-align: center;
  margin: 0;
  text-shadow: 0 2px 4px rgba(189, 4, 7, 0.1);
}

/* 课程操作区域 */
.course-actions-section {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 40px;
}

/* 胶囊容器 */
.course-action-capsule {
  display: flex;
  align-items: stretch;
  background: white;
  border-radius: 40px;
  overflow: hidden;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.1);
  border: 2px solid #BD0407;
}

/* 课程代码部分 */
.course-code-part {
  background: linear-gradient(135deg, #BD0407, #E85A4F);
  color: white;
  padding: 14px 28px;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  letter-spacing: 0.5px;
}

/* 统一的按钮样式 */
.action-btn {
  border: none;
  padding: 14px 36px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  letter-spacing: 0.3px;
}

/* 开始上课按钮 */
.start-action {
  background: linear-gradient(135deg, #BD0407, #E85A4F);
  color: white;
}

.start-action:hover {
  background: linear-gradient(135deg, #A00306, #D03639);
  transform: scale(1.02);
}

.start-action:active {
  transform: scale(0.98);
}

/* 结束课堂按钮 */
.end-action {
  background: linear-gradient(135deg, #FF9800, #F57C00);
  color: white;
}

.end-action:hover {
  background: linear-gradient(135deg, #F57C00, #E65100);
  transform: scale(1.02);
}

.end-action:active {
  transform: scale(0.98);
}

/* 课堂已结束指示器 */
.ended-action {
  background: linear-gradient(135deg, #9E9E9E, #757575);
  color: white;
  cursor: default;
  opacity: 0.8;
}

/* 按钮内阴影效果 */
.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, transparent 50%, rgba(0,0,0,0.1) 100%);
  pointer-events: none;
}

/* 悬停时的高光效果 */
.action-btn:hover::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: shimmer 0.6s ease-out;
}

@keyframes shimmer {
  0% { opacity: 0; transform: scale(0.5); }
  50% { opacity: 1; transform: scale(1); }
  100% { opacity: 0; transform: scale(1.2); }
}

/* 自定义阶段时间线 */
.custom-stages-timeline {
  margin-bottom: 40px;
}

/* 阶段步骤条 */
.stages-steps {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 60px;
  position: relative;
}

.stage-step-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stage-step-wrapper:hover .stage-circle {
  transform: scale(1.1);
}

.stage-step-wrapper.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.stage-step-wrapper.disabled:hover .stage-circle {
  transform: none;
}

.stage-step {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  position: relative;
}

/* 阶段圆圈 */
.stage-circle {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  font-weight: bold;
  color: rgba(95, 106, 121, 1);
  background: #fff;
  border: 3px solid rgba(95, 106, 121, 1);
  box-shadow: 0 10px 25px rgba(255, 151, 151, 0.25);
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.stage-circle.active {
  color: rgba(189, 4, 7, 1);
  background: #fff;
  border: 3px solid rgba(189, 4, 7, 1);
  box-shadow: 0 10px 25px rgba(255, 151, 151, 0.25);
}

/* 连接线 */
.stage-line {
  position: absolute;
  top: 50%;
  left: 60%;
  width: 80%;
  height: 8px;
  background: #D4CCC4;
  transform: translateY(-50%);
  z-index: 1;
  border-radius: 4px;
}

.stage-line.active {
  background: #BD0407;
}

/* 阶段标题 */
.stage-step-wrapper .stage-title {
  margin-top: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #666;
  text-align: center;
  transition: color 0.3s ease;
}

.stage-step-wrapper.active .stage-title,
.stage-title.active {
  color: #BD0407;
}

/* 阶段内容卡片区域 */
.stages-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
  margin-top: 40px;
}

/* 阶段内容卡片 */
.stage-content-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0px 10px 20px 0px rgba(255, 152, 152, 0.25);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.stage-content-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(255, 152, 152, 0.3);
}

.stage-content-card.selected {
  border-color: #BD0407;
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(189, 4, 7, 0.2);
}

/* 卡片头部 */
.stage-card-header {
  padding: 20px 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 活跃状态的标题头部（进行中和已完成） */
.stage-card-header.header-active {
  background: linear-gradient(180.00deg, rgba(212, 50, 42, 1),rgba(177, 36, 26, 1) 100%);
}

/* 待完成状态的标题头部 */
.stage-card-header.header-pending {
  background: linear-gradient(180.00deg, rgba(178, 179, 187, 1), rgba(95, 106, 121, 1) 100%);
}

.stage-title-text {
  font-size: 18px;
  font-weight: bold;
  color: white;
  text-align: center;
  width: 100%;
}

/* 卡片内容 */
.stage-card-content {
  padding: 24px;
  background: white;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.stage-card-content:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.stage-card-content p {
  margin: 0;
  line-height: 1.6;
  color: rgba(67, 67, 67, 1);
}

.stage-card-content .no-content {
  color: rgba(67, 67, 67, 0.6);
  font-style: italic;
}

/* 阶段文本内容限制高度 */
.stage-text-content {
  max-height: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
  position: relative;
}

.stage-text-content::after {
  content: "";
  position: absolute;
  bottom: 0;
  right: 0;
  width: 30px;
  height: 20px;
  background: linear-gradient(to right, transparent, white);
}



/* 无阶段提示 */
.no-stages {
  text-align: center;
  padding: 80px 20px;
  color: #666;
}

.no-stages i {
  font-size: 64px;
  color: #ddd;
  margin-bottom: 16px;
}

.no-stages h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  color: #999;
}

.no-stages p {
  margin: 0;
  color: #ccc;
}

/* 内容详情弹窗样式 */
.stage-content-detail {
  max-height: 400px;
  overflow-y: auto;
  line-height: 1.8;
  color: #333;
  font-size: 14px;
}

.stage-content-detail p {
  margin: 0;
  word-break: break-word;
  white-space: pre-line;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    padding: 20px;
    max-width: none;
  }
  
  .course-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .stages-content {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 40px;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 16px;
  }
  
  .stages-container {
    padding: 20px;
  }
  
  .course-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .course-meta {
    flex-direction: column;
    gap: 8px;
  }
  
  .course-actions-section {
    flex-direction: column;
    gap: 20px;
  }
  
  .stages-steps {
    flex-direction: column;
    gap: 30px;
  }
  
  .stage-step {
    flex-direction: column;
    width: auto;
  }
  
  .stage-line {
    display: none;
  }
  
  .stages-content {
    grid-template-columns: 1fr;
  }
  
  .stage-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .stage-time-info {
    flex-direction: column;
    gap: 8px;
  }
}
</style> 