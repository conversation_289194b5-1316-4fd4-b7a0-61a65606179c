<template>
  <div style="display: flex;">
    <!-- 左侧教学系统分组 -->
    <div style="border-right: 1px solid #E8E8E8; width: 300px;">
      <div>
        <div>教学系统分组
         <el-popconfirm title="AI分组，会先清空旧的分组信息，您确定分组吗?" @confirm="doaigroup">
          <template #reference>
            <el-button type="primary">AI分组</el-button>
          </template>
        </el-popconfirm>
         

        </div>
        <div class="demo-collapse" style="margin-top: 5px;">
          <el-collapse>
            <el-collapse-item
                v-for="(courseTeGroup, groupIndex) in courseTeGroupList"
                :title="courseTeGroup.GROUP_NAME + '（' + courseTeGroup.USER_COUNT + '）'"
                :key="groupIndex"
            >
              <div>
                <VueDraggable  @end="studentDragableEnd" ref="el" group="student" style="display: flex;flex-wrap: wrap;gap: 2px;" v-model="courseTeGroup.STUDENT_LIST" :id="courseTeGroup.RESOURCE_ID" :key="courseTeGroup.RESOURCE_ID" >
                  <div v-for="item in courseTeGroup.STUDENT_LIST" :key="item.RESOURCE_ID" style="margin-right: 4px;margin-top: 4px;border-radius: 4px;" >
                    <el-tag v-if="item.STUDENT_SEX ==='1' " ><i class="el-icon custom-icon-boy"></i><span>{{ item.STUDENT_NAME }}</span></el-tag>
                    <el-tag v-if="item.STUDENT_SEX ==='2' " type="danger"><i class="el-icon custom-icon-girl"></i><span>{{ item.STUDENT_NAME }}</span></el-tag>
                  </div>
                </VueDraggable>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </div>

    <!-- 右侧课程小组 -->
    <div style="width: 100%;">
      <div style="width: 100%; display: grid; grid-template-columns: 1fr 1fr;">
        <div style="display: contents;">
          <div
              v-for="(courseGroup, index) in courseGroupList"
              :key="index"
              style="margin: 0 10px 10px 10px; box-sizing: border-box; border: 1px solid rgb(232, 232, 232); border-radius: 8px; background: rgb(255, 255, 255);"
          >
            <div style="padding: 0px 20px 20px 10px;">
              <div style="margin-top: 20px;">
                <div>
                  <div style="font-size: 16px; display: flex; justify-content: space-between; font-weight: 700;">
                    <div :title="courseGroup.groupName" style="overflow: hidden; white-space: nowrap;text-overflow: ellipsis;">{{ courseGroup.groupName }}</div>
                    <div style="display: flex;">
                      <el-space wrap :size="4">
                        <el-button link type="primary" icon="Edit" @click="handleEditCourseGroup(courseGroup)">修改</el-button>
                        <el-button link type="primary" icon="Delete" @click="handleDelCourseGroup(courseGroup)">删除</el-button>
                        <el-button link type="primary" icon="Plus" @click="showAddPuppet(courseGroup)">添加马甲</el-button>
                      </el-space>
                    </div>
                  </div>
                  <div style="display: flex; flex-wrap: wrap;">
                    <div
                        v-for="(coursePuppet, puppetIndex) in courseGroup.puppets"
                        :key="puppetIndex"
                        style="width: max-content; border-radius: 4px; border: 1px solid rgb(241, 241, 241); margin: 2px; text-align: center;"
                    >
                      <div style="display: flex; justify-content: center; align-items: center; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; width: 114px; background: rgb(241, 241, 241); padding: 8px;">
                        <div
                            style="flex: 1; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;"
                            :title="coursePuppet.puppetName"
                            @click="showEditPuppet(coursePuppet)"
                        >
                          {{ coursePuppet.puppetName }}
                        </div>
                        <div style="cursor: pointer; margin-left: 4px;" @click="handleDelPuppet(coursePuppet)">
                          <Close style="width: 10px;" />
                        </div>
                      </div>
                      <div style="padding: 8px; display: grid;min-height: 36px;">
                        <VueDraggable @end="puppetDragableChane" group="student" v-model="coursePuppet.students" :key="coursePuppet.puppetId" :id="coursePuppet.puppetId" >
                          <div v-for="item in coursePuppet.students" :key="item.RESOURCE_ID" style="margin-right: 4px;margin-top: 4px;border-radius: 4px;" >
                            <el-tag v-if="item.studentSex ==='1' " closable  @close="handleDeleteCourseStudent(item)"><i class="el-icon custom-icon-boy"></i><span>{{ item.studentName }}</span></el-tag>
                            <el-tag v-if="item.studentSex ==='2' " closable @close="handleDeleteCourseStudent(item)" type="danger"><i class="el-icon custom-icon-girl"></i><span>{{ item.studentName }}</span></el-tag>
                          </div>
                        </VueDraggable>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div style="margin-left: 20px;">
        <el-button type="primary" @click="handleAddCourseGroup">添加小组</el-button>
      </div>
    </div>
  </div>
  <!--小组-->
  <el-dialog :show-close="false" :title="groupTitle" v-model="groupOpen" width="500px" append-to-body>
    <el-form ref="groupRef" :model="groupForm" :rules="groupRules" label-width="80px">
      <el-form-item label="小组名称" prop="groupName">
        <el-input v-model="groupForm.groupName" placeholder="请输入小组名称" />
      </el-form-item>
      <el-form-item label="序号" prop="groupOrder">
        <el-input-number style="width: 100%;" v-model="groupForm.groupOrder" placeholder="请输入排序号" controls-position="right" :min="0" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="submitCourseGroupForm">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 添加或修改课程内的马甲对话框 -->
  <el-dialog :show-close="false" :title="puppetTitle" v-model="puppetOpen" width="500px" append-to-body>
    <el-form ref="puppetRef" :model="puppetForm" :rules="puppetRules" label-width="80px">
      <el-form-item label="马甲名称" prop="puppetName">
        <el-input v-model="puppetForm.puppetName" placeholder="请输入马甲名称" />
      </el-form-item>
      <el-form-item label="马甲图标" prop="puppetIcon">
        <el-popover
            placement="bottom-start"
            :width="540"
            trigger="click"
        >
          <template #reference>
            <el-input v-model="puppetForm.puppetIcon" placeholder="点击选择马甲图标" @blur="showSelectIcon" readonly>
              <template #prefix>
                <img v-if="puppetForm.puppetIcon" :src="'/dev-api/profile/puppet/' + puppetForm.puppetIcon + '.png'" style="height: 20px;width: 16px;" alt=""/>
                <el-icon v-else style="height: 32px;width: 16px;"><search /></el-icon>
              </template>
            </el-input>
          </template>
          <IconPuppetSelect ref="iconSelectRef" @selected="selected" :active-icon="puppetForm.icon" />
        </el-popover>
      </el-form-item>
      <el-form-item label="马甲描述" prop="puppetDesc">
        <el-input type="textarea" v-model="puppetForm.puppetDesc" placeholder="请输入马甲描述" />
      </el-form-item>
      <el-form-item label="排序号" prop="puppetIndex">
        <el-input-number style="width: 100%;" v-model="puppetForm.puppetIndex" placeholder="请输入排序号" controls-position="right" :min="0" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" @click="submitCoursePuppetForm">确定</el-button>
      </div>
    </template>
  </el-dialog>



    <CodeOutputPanel
    v-model:modelValue="panelOpen"
    :content="outputContent"
    panel-title="AI智能分组"
    :drawer-width="700"  
    :typing-speed="40"   
    />
</template>

<script setup>
import { onMounted, ref,watch} from 'vue'
import IconPuppetSelect from "@/components/IconPuppetSelect";
import { VueDraggable } from 'vue-draggable-plus'
import { Close } from '@element-plus/icons-vue'
import {
  addCourseGroup, delCourseGroup,
  getCourseGroup,
  listGroup,
  listTeCourseGroup,
  updateCourseGroup
} from "@/api/scenario/coursegroup";
import {useRoute} from "vue-router";
import {addStudent, delStudent, updateStudent} from "@/api/scenario/coursestudent";
import {
  addCoursePuppet,
  delCoursePuppet,
  getCoursePuppet,
  updateCoursePuppet
} from "@/api/scenario/coursepuppet";
import {ElMessage,ElLoading} from "element-plus";
const route = useRoute()
const { proxy } = getCurrentInstance()

const courseTeGroupList = ref([])
const courseGroupList = ref([])

const groupOpen = ref(false)
const groupTitle = ref("")
const puppetOpen = ref(false)
const puppetTitle = ref("")
const iconSelectRef = ref(null);

const data = reactive({
  groupForm: {},
  groupRules: {
    groupOrder: [{ required: true, message: "排序号不能为空", trigger: "blur" }],
    groupName: [{ required: true, message: "小组名称不能为空", trigger: "blur" }]
  },
  puppetForm: {},
  puppetRules: {
    puppetIndex: [{ required: true, message: "排序号不能为空", trigger: "blur" }],
    puppetName: [{ required: true, message: "马甲名称不能为空", trigger: "blur" }],
    puppetIcon: [{ required: true, message: "马甲图标不能为空", trigger: "blur" }],
    puppetDesc: [{ required: true, message: "马甲描述不能为空", trigger: "blur" }]
  }

})
const {groupForm,groupRules,puppetForm,puppetRules } = toRefs(data)

//----ai 分组 start------------------
import {aiGroup} from "@/api/scenario/aigenerate.js";

const doaigroup1=()=>{

   const loading = ElLoading.service({
    lock: true,
    text: '正在生成...',
    background: 'rgba(0, 0, 0, 0.7)',
  })

  aiGroup({courseid: route.params.courseId
  }).then(response => {
     loading.close()
    if(response.code==200)
    {
      
      ElMessage.success('分组完成');
       getGroupList()
    }
    else{
     ElMessage.error('分组失败');
    }
  }).catch(()=>{
     loading.close()
  })
}


//-- 改成flow样式
import CodeOutputPanel from '@/components/aiflowpanel/CodeOutputPanel.vue';
import { getToken } from '@/utils/auth'
import { EventSourcePolyfill } from "event-source-polyfill";

const outputContent = ref('');
const panelOpen = ref(false);
const isLoading = ref(false);

const baseURL = import.meta.env.VITE_APP_BASE_API;
// const baseURL = 'http://localhost:8080';
const token=getToken();
var eventSource=null;
// 生成响应
const doaigroup = () => {
  if (!route.params.courseId || isLoading.value) return;
  
  isLoading.value = true;
  outputContent.value = '';
  panelOpen.value = true;

  let loading=null;
  try {
    // 使用EventSource接收流式响应
     eventSource = new EventSourcePolyfill(baseURL+`/scenario/ai/groupflow/generate?courseid=${route.params.courseId}`, 
                          {
                            headers: {
                              'Authorization': `Bearer ${token}`
                            }
                          });
    eventSource.onmessage = (event) => {
        
        
          if (event.data === '[OVER]') {
                eventSource.close();
                panelOpen.value = false;
                if(loading)
                {
                  loading.close();
                }
                 ElMessage.success('分组完成');
                  getGroupList()
                return;
          }
          else if (event.data === '[DONE]') {
              // eventSource.close();
              // isLoading.value = false;
              loading = ElLoading.service({
                lock: true,
                text: 'AI分析完成，正在处理数据...',
              
              })

            return;
          }
          else if(event.data.startsWith('[ERROR]'))
          {
              ElMessage.error(event.data);
               eventSource.close();
                panelOpen.value = false;
                return;
          }
      
      try {
      
          // 处理SSE数据格式：去掉可能的"data:"前缀，清理空白字符
        let content = event.data.trim();
        // 移除可能的"data:"前缀（有些服务器会带这个标识）
        if (content.startsWith('data:')) {
          content = content.slice(5).trim(); // 从第5个字符开始截取（跳过"data:"）
        }
        console.log(content)
        // 更新输出内容
        outputContent.value += content || '';
      
      } catch (error) {
        console.error('解析响应失败:', error);
        outputContent.value += `\n\n[解析错误: ${error.message}]`;
      }
    };
    
    eventSource.onerror = (error) => {
      console.error('EventSource错误:', error);
      eventSource.close();
      outputContent.value += `\n\n[连接错误: ${error}]`;
      isLoading.value = false;
       panelOpen.value = false;
         ElMessage.error('分组错误：'+error);
    };
    
  } catch (error) {
    console.error('生成响应时出错:', error);
    outputContent.value = `错误: ${error.message}`;
    isLoading.value = false;
  }
};


watch( () => panelOpen.value, (newVal) => {
    if (!newVal) { // 抽屉关闭时
      // 停止所有可能的异步更新
      if (eventSource) {
        eventSource.close();
        eventSource = null;
      }
      // 重置状态，避免残留更新
      outputContent.value = '';
      isLoading.value = false;
    }
  }
);

//----ai 分组 end ------------------



// 表单重置
function reset() {
  groupForm.value = {
  }
  puppetForm.value = {
  }
  proxy.resetForm("groupRef")
  proxy.resetForm("puppetRef")
}

function cancel(){
  groupOpen.value = false
  puppetOpen.value = false
  reset()
}

function getGroupList(courseId){
  if(!courseId){
    courseId = route.params.courseId
  }
  listTeCourseGroup(courseId).then(response => {
    courseTeGroupList.value = response.data
  })
  listGroup({courseId:courseId}).then(response => {
    courseGroupList.value = response.data
  })
}
// 初始化加载数据
onMounted(() => {
  const courseId = route.params.courseId
  if (courseId) {
    getGroupList(courseId)
  }
})

// 方法定义
function submitCoursePuppetForm() {
  proxy.$refs["puppetRef"].validate(valid => {
    if (valid) {
      if (puppetForm.value.puppetId != null) {
        updateCoursePuppet(puppetForm.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          puppetOpen.value = false
          getGroupList()
          reset()
        })
      } else {
        addCoursePuppet(puppetForm.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          puppetOpen.value = false
          getGroupList()
          reset()
        })
      }
    }
  })
}
function submitCourseGroupForm() {
  proxy.$refs["groupRef"].validate(valid => {
    if (valid) {
      if (groupForm.value.groupId != null) {
        updateCourseGroup(groupForm.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          groupOpen.value = false
          getGroupList()
          reset()
        })
      } else {
        const courseId = route.params && route.params.courseId
        groupForm.value.courseId = courseId
        addCourseGroup(groupForm.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          groupOpen.value = false
          getGroupList()
          reset()
        })
      }
    }
  })
}

const studentDragableEnd = (evt) => {
  addStudent({studentCode: evt.clonedData.STUDENT_CODE,studentIndex: evt.newIndex, puppetId: evt.to.id}).then(async () => {
    await getGroupList()
    proxy.$modal.msgSuccess("操作成功")
  }).catch(() => {
  });
}

const puppetDragableChane = (event) => {
  let student = event.clonedData
  student.puppetId =  event.to.id
  student.studentIndex = event.newIndex
  updateStudent(student).then(async () => {
    await getGroupList()
    proxy.$modal.msgSuccess("操作成功")
  }).catch(() => {
  });
}

function handleEditCourseGroup(group){
  const _groupId = group.groupId
  getCourseGroup(_groupId).then(response => {
    groupForm.value = response.data
    groupOpen.value = true
    groupTitle.value = "修改场景分组"
  })
}
function handleDelCourseGroup (group){
  const _groupIds = group.groupId
  proxy.$modal.confirm('是否确认删除？').then(function() {
    return delCourseGroup(_groupIds)
  }).then(() => {
    getGroupList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

function showEditPuppet(puppet){
  const _puppetId = puppet.puppetId
  getCoursePuppet(_puppetId).then(response => {
    puppetForm.value = response.data
    puppetOpen.value = true
    puppetTitle.value = "修改马甲"
  })
}

function showAddPuppet(group){
  puppetOpen.value = true
  puppetTitle.value = "添加马甲"
  puppetForm.value.groupId = group.groupId
}

function handleDelPuppet(puppet) {
  const _puppetId = puppet.puppetId
  proxy.$modal.confirm('是否确认删除？').then(function() {
    return delCoursePuppet(_puppetId)
  }).then(() => {
    getGroupList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

const handleDeleteCourseStudent = (student) => {
  delStudent(student.studentId).then(async () => {
    await getGroupList()
    proxy.$modal.msgSuccess("操作成功")
  }).catch(() => {
  });
}

function handleAddCourseGroup(){
  groupOpen.value = true
  groupTitle.value = "添加小组"
}

function selected(name) {
  puppetForm.value.puppetIcon = name;
}

/** 展示下拉图标 */
function showSelectIcon() {
  iconSelectRef.value.reset();
}


</script>

<style scoped>
.custom-icon-boy {
  background-image: url('/src/assets/icons/svg/box.png');
  background-size: cover;
  width: 16px;
  display: inline-block;
}
.custom-icon-girl {
  background-image: url('/src/assets/icons/svg/girl.png');
  background-size: cover;
  width: 16px;
  display: inline-block;
}
/* 可以添加组件特定的样式 */
</style>

<style>
.el-input__inner{
  text-align: left !important;
}
</style>