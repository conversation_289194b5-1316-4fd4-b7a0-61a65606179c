<template>
  <div class="">
    <div class="" v-if="surveys.length > 0">
      <!-- 遍历问卷列表 -->
      <div v-for="(survey, index) in surveys" :key="survey.paperId" class="survey-item">
        <h3 class="survey-title">
          <!-- 问卷标题操作按钮 -->
          <div v-if="showSend">
            <span style="font-weight: 700;">问卷{{ index+1 }}：{{ survey.paperTitle }}</span>
            <span class="action-btn" @click="editSurveyTitle(survey)">{{survey.editingTitle?'关闭':'编辑'}}</span> |
            <span class="action-btn" @click="deleteSurvey(survey.paperId, index)">删除</span> |
            <span class="action-btn"  @click="sendSurvey(survey)">发送问卷</span> |
            <span class="action-btn" @click="sendHistory(survey)">下发记录</span>
          </div>
          <div v-else>
            <span style="font-weight: 700;">问卷{{ index+1 }}：{{ survey.paperTitle }}</span>
            <span class="action-btn" @click="editSurveyTitle(survey)">{{survey.editingTitle?'关闭':'编辑'}}</span> |
            <span class="action-btn" @click="deleteSurvey(survey.paperId, index)">删除</span>
          </div>
        </h3>
        <!-- 问卷标题编辑对话框 -->
        <div v-show="survey.editingTitle" class="edit-title-dialog">
          <input v-model="survey.newPaperTitle" class="edit-title-input" />
          <button @click="saveSurveyTitle(survey)" class="save-btn">保存</button>
        </div>
        <!-- 遍历问卷题目 -->
        <div  v-show="survey.editingTitle" v-for="(question, qIndex) in survey.questions" :key="question.questionId" class="question-item">
          <div @mouseover="showQuestionActions(survey.paperId, question.questionId)" @mouseleave="hideQuestionActions(survey.paperId, question.questionId)">
            <div v-show="!question.editing" >
                <p class="question-text">{{ qIndex + 1 }}、{{ question.questionText }}</p>
                  <div style="padding: 5px;">
                        <div v-for="(option, oIndex) in question.options" :key="question.questionId" >
                          <!-- <el-radio disabled > {{ option.itemText }}</el-radio> -->
                             <el-checkbox  :label="option.itemText" />
                        </div>
                   </div>
            </div>
          
            <!-- 题目操作按钮  v-show="question.showActions" -->
            
            <!-- 题目编辑对话框   -->
            <div v-show="question.editing" class="edit-question-dialog">
                <el-input v-model="question.newQuestionText" type="textarea" />
              <!-- <input v-model="question.newQuestionText" class="edit-question-input" /> -->
              <div v-for="(option, oIndex) in question.options" :key="question.questionId" class="option-item" style="margin-top: 5px;">
                 <el-input v-model="option.itemText" style="width: 440px;"/>
                <!-- <input v-model="option.itemText" class="option-input" /> -->
                <span class="action-btn" @click="deleteOption(survey.paperId, question.questionId, option.itemId, oIndex)">删除</span>
              </div>
              <button @click="addOption(survey.paperId, question)" class="add-option-btn">添加选项</button>
              <button @click="saveQuestion(survey, question)" class="save-question-btn">保存</button>
            </div>
            <div class="question-actions">
              <!-- CopyDocument,Edit,Delete,Top,Bottom,Upload,Download -->
              <el-button-group>
                      <el-button size="small" :icon="Edit"  @click="editQuestion(survey, question)">编辑</el-button>
                      <el-button size="small" :icon="CopyDocument" @click="copyQuestion(survey, question)">复制</el-button>
                      <el-button size="small" :icon="Delete" @click="deleteQuestion(survey.paperId, question.questionId, qIndex)">删除</el-button>
                      <el-button size="small" :icon="Top" @click="moveQuestion(survey.paperId, question.questionId, 'up')">上移</el-button>
                      <el-button size="small" :icon="Bottom" @click="moveQuestion(survey.paperId, question.questionId, 'down')">下移</el-button>
                      <el-button size="small" :icon="Upload" @click="moveQuestion(survey.paperId, question.questionId, 'top')">最前</el-button>
                      <el-button size="small" :icon="Download" @click="moveQuestion(survey.paperId, question.questionId, 'bottom')">最后</el-button>
              </el-button-group>
              <!-- <span class="action-btn" @click="editQuestion(survey, question)">编辑</span> |
              <span class="action-btn" @click="copyQuestion(survey, question)">复制</span> |
              <span class="action-btn" @click="deleteQuestion(survey.paperId, question.questionId, qIndex)">删除</span> |
              <span class="action-btn" @click="moveQuestion(survey.paperId, question.questionId, 'up')">上移</span> |
              <span class="action-btn" @click="moveQuestion(survey.paperId, question.questionId, 'down')">下移</span> |
              <span class="action-btn" @click="moveQuestion(survey.paperId, question.questionId, 'top')">最前</span> |
              <span class="action-btn" @click="moveQuestion(survey.paperId, question.questionId, 'bottom')">最后</span> -->
            </div>
          </div>
        </div>
        <!-- 新增题目按钮 -->
        <div class="add-group-btn" style="margin-top: 5px;">
          <el-button type="primary" @click="addQuestion(survey)" :icon="Plus" plain>新增题目</el-button>
          <el-button type="primary" @click="showBatchAddDialog(survey)" :icon="MessageBox" plain>批量添加题目</el-button>
        </div>
      </div>
      <!-- 新增问卷按钮 -->
      <div class="add-group-btn" style="text-align: center;">
        <el-button type="primary" @click="addSurvey">新增问卷</el-button>
         <el-button type="primary" @click="aiDialogVisible=true">AI生成</el-button>
      </div>

    </div>

    <!-- 无数据时显示空状态 -->
    <div v-else class="empty-container" style="padding: 20px;">
      <div class="add-group-btn" style="text-align: center;">
        <el-button type="primary" @click="addSurvey">新增问卷</el-button>
         <el-button type="primary" @click="aiDialogVisible=true">AI生成</el-button>
      </div>
      <el-empty description="暂无数据"></el-empty>
    </div>
    <!-- 批量添加题目弹出框 -->
    <el-dialog :show-close="false" v-model="batchDialogVisible" :title="currentSurvey ? '批量添加题目 - ' + currentSurvey.paperTitle : '批量添加题目'" width="60%">
      <div class="batch-add-section">
        <h3 class="batch-add-title">批量加入题目</h3>
        <el-alert
            title="每行一个题目，格式：题目文本|选项1,选项2,..."
            type="info"
            :closable="false"
            style="margin-bottom: 15px;"
        />
        <textarea
            v-model="batchQuestions"
            class="batch-input"
            placeholder="示例：&#10;你喜欢什么颜色？|红色,蓝色,绿色&#10;你最喜欢的季节是？|春天,夏天,秋天,冬天"
        ></textarea>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="batchAddQuestions">确认添加</el-button>
          <el-button @click="batchDialogVisible = false">取消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>

  <SelectGroupAndUserDialog
      v-model="dialogVisible"
      :group-tree="groupTreeData"
      :select-type="'user'"
      @submit="handleQuestionUserSubmit"
  />
  <HistoryDialog
      v-model="dialogVisibleHistory"
      title="问卷调查下发记录"
      :showSearch="false"
      :tasks="taskList"
      @status-click="openStat"
      @close="handleDialogClose"
  />


   <!-- AI 生成 -->
    <el-dialog :show-close="false" v-model="aiDialogVisible" title="AI智能组卷" width="380px">
      <div class="batch-add-section">
        <!-- <h3 class="batch-add-title">AI 生成</h3> -->
        <el-form :model="aiform" label-width="auto">
            <el-form-item label="问卷主题">
              <el-input v-model="aiform.topic" type="textarea" placeholder="请输入问卷的主题" />
            </el-form-item>
            <el-form-item label="题目数量">
              <el-input-number style="width: 100%;"  controls-position="right" v-model="aiform.num" :min="1" :max="1000" />
            </el-form-item>

        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="aiDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="aiAddQuestions">确认生成</el-button>
        </span>
      </template>
    </el-dialog>

     <CodeOutputPanel
    v-model:modelValue="panelOpen"
    :content="outputContent"
    panel-title="AI智能组卷"
    :drawer-width="700"  
    :typing-speed="40"   
    />
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage, ElMessageBox,ElLoading  } from 'element-plus';
import SelectGroupAndUserDialog from '@/views/scenario/SelectGroupAndUserDialog.vue'
import {saveSendSurvey} from "@/api/scenario/coursetestpaper";
import {listGroupWithStudent} from "@/api/scenario/coursegroup";
import HistoryDialog from "@/views/scenario/HistoryDialog.vue";
import {getAllList} from "../../api/scenario/coursetestpaper";

import { Search,CopyDocument,Edit,Delete,Top,Bottom,Upload,Download,Plus,MessageBox } from '@element-plus/icons-vue'

const router = useRouter();

const props = defineProps({
  // 类型，course or scene
  type: {
    type: String,
    required: true,
    validator: (value) => ['course', 'scene'].includes(value)
  },
  // 场景ID或课程ID
  id: {
    type: [String, Number],
    required: true
  },
  showSend: {
    type: Boolean,
    required: false,
    default: false
  }
});

const emit = defineEmits(['survey-change']);

const route = useRoute();

// ai生成 20250706
const aiform=ref({
  topic:'',
  num:1
})

const aiDialogVisible=ref(false)
const groupTreeData=ref([])

const aiAddQuestions1=async () => {
  aiform.value.sceneid=props.id;

  const loading = ElLoading.service({
    lock: true,
    text: '正在生成...',
    background: 'rgba(0, 0, 0, 0.7)',
  })

  try {
    const aiTestpaper = await getApiMethod('aiTestpaper');
    const response =await aiTestpaper(aiform.value);

    const newSurvey =response.data;
      
    fetchSurveys();
    // surveys.value.push(newSurvey);
    aiDialogVisible.value=false;
    loading.close()
    ElMessage.success('问卷生成成功');
    // emit('survey-change', surveys.value);
  } catch (error) {
    console.error('生成问卷失败:', error);
    ElMessage.error('生成问卷失败');
  }
};

//20250718 -------------改成flow---------------
import CodeOutputPanel from '@/components/aiflowpanel/CodeOutputPanel.vue';
import { getToken } from '@/utils/auth'
import { EventSourcePolyfill } from "event-source-polyfill";

const outputContent = ref('');
const panelOpen = ref(false);
const isLoading = ref(false);

const baseURL = import.meta.env.VITE_APP_BASE_API;
// const baseURL = 'http://localhost:8080';
const token=getToken();
var eventSource=null;
// 生成响应
const aiAddQuestions = () => {
  if (!props.id || isLoading.value) return;
   aiDialogVisible.value=false;

  isLoading.value = true;
  outputContent.value = '';
  panelOpen.value = true;

  let loading=null;
  try {
    let url=baseURL+`/scenario/ai/testpaperflow/`;
    if (props.type === 'scene') {
      url += `generate?sceneid=${props.id}`;
    } else {
      url += `generate/course?courseid=${props.id}`;
    }
    url+= `&topic=${encodeURIComponent(aiform.value.topic)}&num=${aiform.value.num}`;
    // 使用EventSource接收流式响应
     eventSource = new EventSourcePolyfill(url, 
                          {
                            headers: {
                              'Authorization': `Bearer ${token}`
                            }
                          });
    eventSource.onmessage = (event) => {
        
        
          if (event.data === '[OVER]') {
                eventSource.close();
                panelOpen.value = false;
                if(loading)
                {
                  loading.close();
                }
                 ElMessage.success('组卷完成');
                    fetchSurveys();
                return;
          }
          else if (event.data === '[DONE]') {
              // eventSource.close();
              // isLoading.value = false;
              loading = ElLoading.service({
                lock: true,
                text: 'AI分析完成，正在处理数据...',
              
              })

            return;
          }
          else if(event.data.startsWith('[ERROR]'))
          {
              ElMessage.error(event.data);
               eventSource.close();
                panelOpen.value = false;
                return;
          }
      
      try {
      
          // 处理SSE数据格式：去掉可能的"data:"前缀，清理空白字符
        let content = event.data.trim();
        // 移除可能的"data:"前缀（有些服务器会带这个标识）
        if (content.startsWith('data:')) {
          content = content.slice(5).trim(); // 从第5个字符开始截取（跳过"data:"）
        }
        console.log(content)
        // 更新输出内容
        outputContent.value += content || '';
      
      } catch (error) {
        console.error('解析响应失败:', error);
        outputContent.value += `\n\n[解析错误: ${error.message}]`;
      }
    };
    
    eventSource.onerror = (error) => {
      console.error('EventSource错误:', error);
      eventSource.close();
      outputContent.value += `\n\n[连接错误: ${error}]`;
      isLoading.value = false;
       panelOpen.value = false;
         ElMessage.error('组卷错误：'+error);
    };
    
  } catch (error) {
    console.error('生成响应时出错:', error);
    outputContent.value = `错误: ${error.message}`;
    isLoading.value = false;
  }
};


watch( () => panelOpen.value, (newVal) => {
    if (!newVal) { // 抽屉关闭时
      // 停止所有可能的异步更新
      if (eventSource) {
        eventSource.close();
        eventSource = null;
      }
      // 重置状态，避免残留更新
      outputContent.value = '';
      isLoading.value = false;
    }
  }
);


// -----------ai end


// 问卷数据
const surveys = ref([]);
const surveyId = ref('');
const batchQuestions = ref('');
const batchDialogVisible = ref(false);
const currentSurvey = ref(null);

const dialogVisible = ref(false);
const dialogVisibleHistory = ref(false);
const taskList = ref([]);




// API 方法 - 根据类型动态选择
const api = {
  scene: {
    listTestpaper: () => import("@/api/scenario/scenetestpaper.js").then(m => m.listTestpaper),
    addTestpaper: () => import("@/api/scenario/scenetestpaper.js").then(m => m.addTestpaper),
    aiTestpaper: () => import("@/api/scenario/aigenerate.js").then(m => m.aiTestpaper),
    updateTestpaper: () => import("@/api/scenario/scenetestpaper.js").then(m => m.updateTestpaper),
    delTestpaper: () => import("@/api/scenario/scenetestpaper.js").then(m => m.delTestpaper),
    listQuestion: () => import("@/api/scenario/scenequestion.js").then(m => m.listQuestion),
    addQuestion: () => import("@/api/scenario/scenequestion.js").then(m => m.addQuestion),
    updateQuestion: () => import("@/api/scenario/scenequestion.js").then(m => m.updateQuestion),
    delQuestion: () => import("@/api/scenario/scenequestion.js").then(m => m.delQuestion),
    updateQuestionOrder: () => import("@/api/scenario/scenequestion.js").then(m => m.updateQuestionOrder),
    listItem: () => import("@/api/scenario/scenequestionitem.js").then(m => m.listItem),
    addItem: () => import("@/api/scenario/scenequestionitem.js").then(m => m.addItem),
    updateItem: () => import("@/api/scenario/scenequestionitem.js").then(m => m.updateItem),
    delItem: () => import("@/api/scenario/scenequestionitem.js").then(m => m.delItem)
  },
  course: {
    listTestpaper: () => import("@/api/scenario/coursetestpaper.js").then(m => m.listTestpaper),
    addTestpaper: () => import("@/api/scenario/coursetestpaper.js").then(m => m.addTestpaper),
    updateTestpaper: () => import("@/api/scenario/coursetestpaper.js").then(m => m.updateTestpaper),
    delTestpaper: () => import("@/api/scenario/coursetestpaper.js").then(m => m.delTestpaper),
    listQuestion: () => import("@/api/scenario/coursequestion.js").then(m => m.listQuestion),
    addQuestion: () => import("@/api/scenario/coursequestion.js").then(m => m.addQuestion),
    updateQuestion: () => import("@/api/scenario/coursequestion.js").then(m => m.updateQuestion),
    delQuestion: () => import("@/api/scenario/coursequestion.js").then(m => m.delQuestion),
    updateQuestionOrder: () => import("@/api/scenario/coursequestion.js").then(m => m.updateQuestionOrder),
    listItem: () => import("@/api/scenario/coursequestionitem.js").then(m => m.listItem),
    addItem: () => import("@/api/scenario/coursequestionitem.js").then(m => m.addItem),
    updateItem: () => import("@/api/scenario/coursequestionitem.js").then(m => m.updateItem),
    delItem: () => import("@/api/scenario/coursequestionitem.js").then(m => m.delItem)
  }
};

// 动态获取API方法
const getApiMethod = async (methodName) => {
  return await api[props.type][methodName]();
};

// 显示批量添加对话框
const showBatchAddDialog = (survey) => {
  currentSurvey.value = survey;
  batchQuestions.value = '';
  batchDialogVisible.value = true;
};

// 获取问卷列表
const fetchSurveys = async () => {
  try {
    const listTestpaper = await getApiMethod('listTestpaper');
    const response = await listTestpaper({ [props.type === 'scene' ? 'sceneId' : 'courseId']: props.id });

    surveys.value = response.data.map(survey => ({
      ...survey,
      editingTitle: false,
      newTitle: '',
      questions: []
    }));

    // 获取每个问卷的题目
    for (const survey of surveys.value) {
      await fetchQuestions(survey.paperId);
    }

    emit('survey-change', surveys.value);
  } catch (error) {
    console.error('获取问卷列表失败:', error);
    ElMessage.error('获取问卷列表失败');
  }
};

// 获取问卷的题目列表
const fetchQuestions = async (surveyId) => {
  try {
    const survey = surveys.value.find(s => s.paperId === surveyId);
    if (!survey) return;

    const listQuestion = await getApiMethod('listQuestion');
    const response = await listQuestion({ paperId: surveyId });

    const listItem = await getApiMethod('listItem');
    survey.questions = await Promise.all(response.data.map(async question => {
      const optionsResponse = await listItem({ questionId: question.questionId });
      return {
        ...question,
        newText: '',
        editing: false,
        showActions: false,
        options: optionsResponse.data.map(option => ({
          ...option,
          editing: false
        }))
      };
    }));
  } catch (error) {
    console.error('获取题目列表失败:', error);
    ElMessage.error('获取题目列表失败');
  }
};

// 编辑问卷标题
const editSurveyTitle = (survey) => {
  if(survey.editingTitle) 
  {
     survey.editingTitle = false;
     return;
  }
  survey.editingTitle = true;
  survey.newPaperTitle = survey.paperTitle;
};

// 保存问卷标题
const saveSurveyTitle = async (survey) => {
  try {
    const updateTestpaper = await getApiMethod('updateTestpaper');
    await updateTestpaper({
      paperId: survey.paperId,
      paperTitle: survey.newPaperTitle
    });

    survey.paperTitle = survey.newPaperTitle;
    survey.editingTitle = false;
    ElMessage.success('问卷标题保存成功');
    emit('survey-change', surveys.value);
  } catch (error) {
    console.error('保存问卷标题失败:', error);
    ElMessage.error('保存问卷标题失败');
  }
};

// 删除问卷
const deleteSurvey = async (surveyId, index) => {
  try {
    await ElMessageBox.confirm('确定要删除这个问卷吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    const delTestpaper = await getApiMethod('delTestpaper');
    await delTestpaper(surveyId);

    surveys.value.splice(index, 1);
    ElMessage.success('问卷删除成功');
    emit('survey-change', surveys.value);
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除问卷失败:', error);
      ElMessage.error('删除问卷失败');
    }
  }
};

function sendSurvey(survey){
  dialogVisible.value = true
  surveyId.value = survey.paperId
  listGroupWithStudent(survey.courseId).then(r=>{
    groupTreeData.value = r.data
  })
}

function sendHistory(survey){
  dialogVisibleHistory.value = true
  getAllList(survey.paperId).then(r=>{
    taskList.value = r.data
  })
}

function openStat(survey){
  // 在新标签页中打开
  debugger
  const routeData = router.resolve({
    path: ("../course-question-stat/"+ survey.paperId)
  });
  window.open(routeData.href, '_blank');
}
function handleQuestionUserSubmit(data){
  const  courseId = route.params?.courseId
  let students = []
  data.selectedStudents.forEach(item => {
    students.push({paperId:surveyId.value,studentCode: item.studentCode})
  })
  debugger
  saveSendSurvey({paperId: surveyId.value,courseId: courseId,paperNotes: data.remark, details: students}).then(
      ElMessage.success('下发成功')
  )
}

// 显示题目操作按钮
const showQuestionActions = (surveyId, questionId) => {
  const survey = surveys.value.find(s => s.paperId === surveyId);
  if (!survey) return;
  const question = survey.questions.find(q => q.questionId === questionId);
  if (question) {
    question.showActions = true;
  }
};

// 隐藏题目操作按钮
const hideQuestionActions = (surveyId, questionId) => {
  const survey = surveys.value.find(s => s.paperId === surveyId);
  if (!survey) return;

  const question = survey.questions.find(q => q.questionId === questionId);
  if (question) {
    question.showActions = false;
  }
};

// 编辑题目
const editQuestion = (survey, question) => {
  question.editing = true;
  question.newQuestionText = question.questionText;
};

// 保存题目
const saveQuestion = async (survey, question) => {
  try {
    // 更新题目内容
    const updateQuestion = await getApiMethod('updateQuestion');
    await updateQuestion({
      questionId: question.questionId,
      questionText: question.newQuestionText,
      paperId: survey.paperId
    });

    // 更新选项
    const updateItem = await getApiMethod('updateItem');
    for (const option of question.options) {
      await updateItem({
        itemId: option.itemId,
        itemText: option.itemText,
        questionId: question.questionId
      });
    }

    question.questionText = question.newQuestionText;
    question.editing = false;
    ElMessage.success('题目保存成功');
    hideQuestionActions(survey.paperId, question.questionId)
  } catch (error) {
    console.error('保存题目失败:', error);
    ElMessage.error('保存题目失败');
  }
};

// 复制题目
const copyQuestion = async (survey, question) => {
  let index = 0;
  if(survey.questions.length > 0){
    index = survey.questions[survey.questions.length-1].questionIndex + 1;
  }

  const data = {
    questionIndex: index,
    questionId: Date.now(),
    questionType: '1',
    questionText: question.questionText + ' (复制)',
    paperId: survey.paperId
  };

  try {
    // 创建新题目
    const addQuestion = await getApiMethod('addQuestion');
    await addQuestion(data);

    const newQuestion = {
      ...data,
      newPaperText: '',
      editing: false,
      showActions: false,
      options: []
    };

    // 复制选项
    const addItem = await getApiMethod('addItem');
    for (const [idx, option] of question.options.entries()) {
      const optionData = {
        itemId: Date.now() + idx,
        itemText: option.itemText,
        questionId: newQuestion.questionId
      };
      await addItem(optionData);
      newQuestion.options.push(optionData);
    }

    survey.questions.push(newQuestion);
    ElMessage.success('题目复制成功');
  } catch (error) {
    console.error('复制题目失败:', error);
    ElMessage.error('复制题目失败');
  }
};

// 删除题目
const deleteQuestion = async (surveyId, questionId, qIndex) => {
  try {
    await ElMessageBox.confirm('确定要删除这个题目吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    const delQuestion = await getApiMethod('delQuestion');
    await delQuestion(questionId);

    const survey = surveys.value.find(s => s.paperId === surveyId);
    if (survey) {
      survey.questions.splice(qIndex, 1);
    }
    ElMessage.success('题目删除成功');
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除题目失败:', error);
      ElMessage.error('删除题目失败');
    }
  }
};

// 移动题目
const moveQuestion = async (surveyId, questionId, direction) => {
  try {
    const survey = surveys.value.find(s => s.paperId === surveyId);
    if (!survey) return;

    const questionIndex = survey.questions.findIndex(q => q.questionId === questionId);
    if (questionIndex === -1) return;

    let newIndex = questionIndex;

    switch (direction) {
      case 'up':
        if (questionIndex > 0) newIndex = questionIndex - 1;
        break;
      case 'down':
        if (questionIndex < survey.questions.length - 1) newIndex = questionIndex + 1;
        break;
      case 'top':
        newIndex = 0;
        break;
      case 'bottom':
        newIndex = survey.questions.length - 1;
        break;
    }

    if (newIndex !== questionIndex) {
      // 先从数组中移除
      const [question] = survey.questions.splice(questionIndex, 1);
      // 插入到新位置
      survey.questions.splice(newIndex, 0, question);

      // 更新排序到服务器
      const updateQuestionOrder = await getApiMethod('updateQuestionOrder');
      await updateQuestionOrder(survey.questions.map(q => q.questionId));
      ElMessage.success('题目移动成功');
    }
  } catch (error) {
    console.error('移动题目失败:', error);
    ElMessage.error('移动题目失败');
  }
};

// 新增题目
const addQuestion = async (survey) => {
  let index = 0;
  if(survey.questions.length > 0){
    index = survey.questions[survey.questions.length-1].questionIndex + 1;
  }

  const data = {
    questionIndex: index,
    questionId: Date.now(),
    questionType: '1',
    questionText: '新题目',
    paperId: survey.paperId
  };

  try {
    const addQuestion = await getApiMethod('addQuestion');
    await addQuestion(data);

    const newQuestion = {
      ...data,
      newPaperTitle: '',
      options: [
        { itemId: Date.now() + 1, itemText: '选项1', questionId: data.questionId },
        { itemId: Date.now() + 2, itemText: '选项2', questionId: data.questionId }
      ],
      editing: false,
      showActions: false
    };

    // 添加默认选项
    const addItem = await getApiMethod('addItem');
    for (const option of newQuestion.options) {
      await addItem(option);
    }

    if(survey.questions === undefined){
      survey.questions = [];
    }
    survey.questions.push(newQuestion);
    ElMessage.success('题目添加成功');
  } catch (error) {
    console.error('新增题目失败:', error);
    ElMessage.error('新增题目失败');
  }
};

// 新增问卷
const addSurvey = async () => {
  const data = {
    paperId: Date.now(),
    [props.type === 'scene' ? 'sceneId' : 'courseId']: props.id,
    paperTitle: '新问卷'
  };

  try {
    const addTestpaper = await getApiMethod('addTestpaper');
    await addTestpaper(data);

    const newSurvey = {
      ...data,
      editingTitle: false,
      newPaperTitle: '',
      questions: []
    };
    surveys.value.push(newSurvey);
    ElMessage.success('问卷添加成功');
    emit('survey-change', surveys.value);
  } catch (error) {
    console.error('新增问卷失败:', error);
    ElMessage.error('新增问卷失败');
  }
};

// 添加选项
const addOption = async (surveyId, question) => {
  const data = {
    itemId: Date.now(),
    itemText: '新选项',
    questionId: question.questionId
  };

  try {
    const addItem = await getApiMethod('addItem');
    await addItem(data);

    question.options.push(data);
    ElMessage.success('选项添加成功');
  } catch (error) {
    console.error('添加选项失败:', error);
    ElMessage.error('添加选项失败');
  }
};

// 删除选项
const deleteOption = async (surveyId, questionId, itemId, oIndex) => {
  try {
    await ElMessageBox.confirm('确定要删除这个选项吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    const delItem = await getApiMethod('delItem');
    await delItem(itemId);

    const survey = surveys.value.find(s => s.paperId === surveyId);
    if (!survey) return;

    const question = survey.questions.find(q => q.questionId === questionId);
    if (question) {
      question.options.splice(oIndex, 1);
    }
    ElMessage.success('选项删除成功');
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除选项失败:', error);
      ElMessage.error('删除选项失败');
    }
  }
};

// 批量添加题目
const batchAddQuestions = async () => {
  if (!currentSurvey.value) return;
  if (!batchQuestions.value.trim()) {
    ElMessage.warning('请输入要批量添加的题目');
    return;
  }

  const survey = currentSurvey.value;
  const lines = batchQuestions.value.split('\n');
  const successCount = ref(0);
  const errorMessages = [];

  for (const line of lines) {
    if (!line.trim()) continue;

    const parts = line.split('|');
    const questionText = parts[0].trim();
    const optionsText = parts.length > 1 ? parts[1].split(',').map(o => o.trim()) : [];

    try {
      // 添加题目
      let index = 0;
      if(survey.questions.length > 0){
        index = survey.questions[survey.questions.length-1].questionIndex + 1;
      }

      const questionData = {
        questionIndex: index,
        questionId: Date.now(),
        questionType: '1',
        questionText: questionText,
        paperId: survey.paperId
      };

      const addQuestion = await getApiMethod('addQuestion');
      await addQuestion(questionData);

      // 添加选项
      const addItem = await getApiMethod('addItem');
      for (const [idx, optionText] of optionsText.entries()) {
        const optionData = {
          itemId: Date.now() + idx,
          itemText: optionText,
          questionId: questionData.questionId
        };
        await addItem(optionData);
      }

      successCount.value++;
    } catch (error) {
      console.error('批量添加题目失败:', error);
      errorMessages.push(`题目 "${questionText}" 添加失败`);
    }
  }

  // 刷新题目列表
  await fetchQuestions(survey.paperId);
  batchDialogVisible.value = false;

  // 显示结果
  if (errorMessages.length === 0) {
    ElMessage.success(`成功添加 ${successCount.value} 个题目`);
  } else {
    const message = `成功添加 ${successCount.value} 个题目，失败 ${lines.length - successCount.value} 个`;
    ElMessageBox.alert(
        `${message}<br/><br/>失败详情：<br/>${errorMessages.join('<br/>')}`,
        '部分题目添加完成',
        {
          dangerouslyUseHTMLString: true,
          showConfirmButton: true
        }
    );
  }
};

// 监听props.id变化
watch(() => props.id, () => {
  if (props.id) {
    fetchSurveys();
  }
});

// 组件挂载时获取问卷列表
onMounted(() => {
  if (props.id) {
    fetchSurveys();
  }
});
</script>

<style scoped>
/* 保持原有样式不变 */
.survey-container {
  font-family: Arial, sans-serif;
  padding: 20px;
  background-color: #f4f4f4;
}

.survey-content {
  background-color: #fff;
  padding: 20px;
  border-radius: 5px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}

.survey-item {
  margin-bottom: 25px;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  padding: 15px;
}

.survey-title {
  margin-top: 0;
  color: #333;
  font-size: 18px;
}

.action-btn {
  color: #c00;
  cursor: pointer;
  margin-left: 5px;
  font-size: 14px;
}

.edit-title-dialog {
  margin-top: 10px;
}

.edit-title-input {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 3px;
  width: 200px;
}

.save-btn {
  background-color: #c00;
  color: #fff;
  border: none;
  padding: 8px 15px;
  border-radius: 3px;
  cursor: pointer;
  margin-left: 10px;
}

.question-item {
  min-height: 70px;
  margin-top: 15px;
  padding: 10px;
  border: 1px solid #f0f0f0;
  border-radius: 3px;
}
.question-item:hover {
  border-color: #c00;
}

.question-text {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.question-actions {
  margin-top: 8px;
  color: #666;
  font-size: 13px;
}

.edit-question-dialog {
  margin-top: 10px;
}

.edit-question-input {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 3px;
  width: 300px;
  margin-bottom: 10px;
}

.option-item {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.option-input {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 3px;
  width: 400px;
  margin-right: 10px;
}

.add-option-btn {
  background-color: #f0f0f0;
  color: #333;
  border: 1px solid #ccc;
  padding: 6px 12px;
  border-radius: 3px;
  cursor: pointer;
  margin-right: 10px;
}

.save-question-btn {
  background-color: #c00;
  color: #fff;
  border: none;
  padding: 8px 15px;
  border-radius: 3px;
  cursor: pointer;
}

.add-question-btn {
  background-color: #c00;
  color: #fff;
  border: none;
  padding: 10px 15px;
  border-radius: 3px;
  cursor: pointer;
  margin-top: 10px;
}

.add-survey-btn {
  background-color: #c00;
  color: #fff;
  border: none;
  padding: 12px 20px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 16px;
  margin-bottom: 20px;
}

.batch-add-section {
  margin-top: 20px;
}

.batch-add-title {
  color: #333;
  font-size: 18px;
  margin-top: 0;
}

.batch-input {
  width: 100%;
  height: 200px;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 3px;
  margin-bottom: 10px;
  font-family: Arial, sans-serif;
  white-space: pre;
}

.dialog-footer button:first-child {
  margin-right: 10px;
}


.dialog-content {
  width: 100%;
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.tree-container {
  flex: 1;
  border: 1px solid #eee;
  padding: 10px;
  border-radius: 4px;
}

.selected-container {
  flex: 1;
  border: 1px solid #eee;
  padding: 10px;
  border-radius: 4px;
}

.remark-container {
  margin-bottom: 20px;
}




</style>