<template>
  <div 
    ref="fabContainer"
    class="screen-fab-container"
    :style="containerStyle"
    @mousedown="handleMouseDown"
    @touchstart="handleTouchStart">
    
    <!-- 悬浮按钮组 -->
    <div class="fab-group">
      <el-tooltip
        v-for="(item, index) in menuItems"
        :key="item.id"
        :content="item.title"
        placement="top"
        effect="dark">
        <el-button
          :class="['fab-button', `fab-button-${index}`]"
          circle
          size="large"
          :id="`fab-button-${item.id}`"
          :style="getFabButtonStyle(index)"
          @click="handleMenuClick(item)">
          <el-icon :size="20">
            <component :is="item.icon" />
          </el-icon>
        </el-button>
      </el-tooltip>
    </div>
  </div>
</template>

<script setup name="ScreenFab">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Monitor, Document, DataAnalysis, Operation, ChatLineSquare } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  // 主按钮颜色
  mainBtnColor: {
    type: String,
    default: '#BD0407'
  },
  // 悬浮按钮位置
  position: {
    type: String,
    default: 'bottom-left',
    validator: (value) => ['bottom-left', 'bottom-right', 'top-left', 'top-right'].includes(value)
  },
  // 自定义菜单项
  customMenuItems: {
    type: Array,
    default: null
  }
})

// 路由
const router = useRouter()
const route = useRoute()

// 状态
const fabContainer = ref(null)
const isDragging = ref(false)
const position = ref({ x: 30, y: 30 })
const dragOffset = ref({ x: 0, y: 0 })

// localStorage 键名
const POSITION_STORAGE_KEY = 'screen-fab-position'

// 默认菜单项
const defaultMenuItems = [
  {
    id: 'stages',
    title: '课程阶段',
    icon: Operation,
    route: 'stages'
  },
  {
    id: 'monitor',
    title: '群聊监控',
    icon: ChatLineSquare,
    route: 'monitor'
  },
  {
    id: 'survey',
    title: '问卷统计',
    icon: Document,
    route: 'survey'
  }
]

// 计算菜单项
const menuItems = computed(() => {
  return props.customMenuItems || defaultMenuItems
})

// 容器样式
const containerStyle = computed(() => {
  const baseStyle = {
    position: 'fixed',
    zIndex: 99,
    userSelect: 'none',
    cursor: isDragging.value ? 'grabbing' : 'grab'
  }
  
  // 根据position设置初始位置
  switch (props.position) {
    case 'bottom-left':
      return { ...baseStyle, bottom: `${position.value.y}px`, left: `${position.value.x}px` }
    case 'bottom-right':
      return { ...baseStyle, bottom: `${position.value.y}px`, right: `${position.value.x}px` }
    case 'top-left':
      return { ...baseStyle, top: `${position.value.y}px`, left: `${position.value.x}px` }
    case 'top-right':
      return { ...baseStyle, top: `${position.value.y}px`, right: `${position.value.x}px` }
    default:
      return { ...baseStyle, bottom: `${position.value.y}px`, left: `${position.value.x}px` }
  }
})

// 获取按钮样式
const getFabButtonStyle = (index) => {
  return {
    backgroundColor: props.mainBtnColor,
    borderColor: props.mainBtnColor,
    width: '56px',
    height: '56px',
    margin: '4px'
  }
}

// 处理鼠标按下
const handleMouseDown = (event) => {
  if (event.target.closest('.fab-button')) return // 如果点击的是按钮，不处理拖拽
  
  isDragging.value = true
  dragOffset.value = {
    x: event.clientX - fabContainer.value.getBoundingClientRect().left,
    y: event.clientY - fabContainer.value.getBoundingClientRect().top
  }
  
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
  event.preventDefault()
}

// 处理触摸开始
const handleTouchStart = (event) => {
  if (event.target.closest('.fab-button')) return
  
  const touch = event.touches[0]
  isDragging.value = true
  dragOffset.value = {
    x: touch.clientX - fabContainer.value.getBoundingClientRect().left,
    y: touch.clientY - fabContainer.value.getBoundingClientRect().top
  }
  
  document.addEventListener('touchmove', handleTouchMove, { passive: false })
  document.addEventListener('touchend', handleTouchEnd)
  event.preventDefault()
}

// 处理鼠标移动
const handleMouseMove = (event) => {
  if (!isDragging.value) return
  
  updatePosition(event.clientX, event.clientY)
  event.preventDefault()
}

// 处理触摸移动
const handleTouchMove = (event) => {
  if (!isDragging.value) return
  
  const touch = event.touches[0]
  updatePosition(touch.clientX, touch.clientY)
  event.preventDefault()
}

// 保存位置到 localStorage
const savePosition = (pos) => {
  try {
    const positionData = {
      x: pos.x,
      y: pos.y,
      position: props.position
    }
    localStorage.setItem(POSITION_STORAGE_KEY, JSON.stringify(positionData))
  } catch (error) {
    console.warn('保存悬浮按钮位置失败:', error)
  }
}

// 从 localStorage 加载位置
const loadPosition = () => {
  try {
    const savedData = localStorage.getItem(POSITION_STORAGE_KEY)
    if (savedData) {
      const positionData = JSON.parse(savedData)
      // 只有在位置模式相同时才恢复位置
      if (positionData.position === props.position) {
        position.value = { x: positionData.x, y: positionData.y }
      }
    }
  } catch (error) {
    console.warn('加载悬浮按钮位置失败:', error)
  }
}

// 更新位置
const updatePosition = (clientX, clientY) => {
  const newX = clientX - dragOffset.value.x
  const newY = clientY - dragOffset.value.y
  
  // 边界检测
  const containerRect = fabContainer.value.getBoundingClientRect()
  const windowWidth = window.innerWidth
  const windowHeight = window.innerHeight
  
  const clampedX = Math.max(0, Math.min(newX, windowWidth - containerRect.width))
  const clampedY = Math.max(0, Math.min(newY, windowHeight - containerRect.height))
  
  // 根据position转换坐标
  switch (props.position) {
    case 'bottom-left':
      position.value = { x: clampedX, y: windowHeight - clampedY - containerRect.height }
      break
    case 'bottom-right':
      position.value = { x: windowWidth - clampedX - containerRect.width, y: windowHeight - clampedY - containerRect.height }
      break
    case 'top-left':
      position.value = { x: clampedX, y: clampedY }
      break
    case 'top-right':
      position.value = { x: windowWidth - clampedX - containerRect.width, y: clampedY }
      break
    default:
      position.value = { x: clampedX, y: windowHeight - clampedY - containerRect.height }
  }
  
  // 保存新位置
  savePosition(position.value)
}

// 处理鼠标松开
const handleMouseUp = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
}

// 处理触摸结束
const handleTouchEnd = () => {
  isDragging.value = false
  document.removeEventListener('touchmove', handleTouchMove)
  document.removeEventListener('touchend', handleTouchEnd)
}

// 处理菜单项点击
const handleMenuClick = (item) => {
  if (item.route) {
    console.log(`path: ${route.path}, 路由: ${item.route}`)
    if (item.route === route.path) {
      ElMessage.info(`当前已在${item.title}页面`)
    } else {
      // 获取当前路由的code参数
      const currentCode = route.query.code
      
      // 构建新的路由，携带code参数
      const targetRoute = {
        path: item.route,
        query: currentCode ? { code: currentCode } : {}
      }
      
      router.push(targetRoute)
      ElMessage.success(`正在跳转到${item.title}`)
    }
  }
  
  // 发射事件给父组件
  emit('fab-click', item)
}

// 组件挂载时加载保存的位置
onMounted(() => {
  loadPosition()
})

// 组件卸载时清理事件监听器
onUnmounted(() => {
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
  document.removeEventListener('touchmove', handleTouchMove)
  document.removeEventListener('touchend', handleTouchEnd)
})

// 事件
const emit = defineEmits(['fab-click'])
</script>

<style scoped>
.screen-fab-container {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.1s ease;
}

.screen-fab-container:active {
  transform: scale(1.02);
}

.fab-group {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.fab-button {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.2s ease !important;
  cursor: pointer !important;
  color: white !important;
}

.fab-button:hover {
  transform: scale(1.05) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

.fab-button:active {
  transform: scale(0.95) !important;
}

/* Tooltip 样式调整 */
:deep(.el-tooltip__popper) {
  z-index: 10000 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .fab-button {
    width: 48px !important;
    height: 48px !important;
  }
  
  .fab-group {
    gap: 6px;
    padding: 6px;
  }
}
</style> 