<template>
  <el-drawer
    :model-value="isOpen"
    :title="panelTitle"
    :size="drawerWidth"
    direction="rtl"
    :with-header="false"
    :close-on-click-modal="false"
    body-class="ai-drawer-panel"
    @close="handleClose"
    @opened="handleAfterEnter"
    @closed="handleAfterLeave"
    
  >
    <!-- 内容展示区域 -->
    <div class="content-container">
      <div>
        <el-button type="plain" loading>大模型正在分析....</el-button>
      </div>
      <div 
        class="response-text" 
        ref="contentRef"
        v-html="formattedContent"
      ></div>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, computed, watch, onBeforeUnmount, nextTick } from 'vue';

// 接收的props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  content: {
    type: String,
    default: ''
  },
  panelTitle: {
    type: String,
    default: 'AI 响应内容'
  },
  typingSpeed: {
    type: Number,
    default: 30,
    validator: (value) => value > 0 && value <= 100
  },
  drawerWidth: {
    type: Number,
    default: 600,
    validator: (value) => value >= 300 && value <= 1200
  }
});

// 定义emits
const emits = defineEmits(['update:modelValue', 'close']);

// 状态管理
const isOpen = ref(props.modelValue);
const displayedContent = ref(props.content);
const isGenerating = ref(false);
const isCompleted = ref(false);
const typingTimer = ref(null);
const contentRef = ref(null);
const isTransitioning = ref(false); // 标记抽屉是否正在过渡中
const currentEventSource = ref(null); // SSE连接引用

// 格式化内容
const formattedContent = computed(() => {
  if (!displayedContent.value) return '<p class="empty-placeholder">等待内容生成...</p>';
  
  return displayedContent.value
    .replace(/\n\n+/g, '</p><p class="content-paragraph">')
    .replace(/\n/g, '<br>')
    .replace(/^/, '<p class="content-paragraph">')
    .replace(/$/, '</p>');
});


// 关闭抽屉
const handleClose = () => {
  // 标记为过渡中
  isTransitioning.value = true;
  
  // 清理定时器和SSE连接
  if (typingTimer.value) {
    clearInterval(typingTimer.value);
    typingTimer.value = null;
  }
  
  if (currentEventSource.value) {
    currentEventSource.value.close();
    currentEventSource.value = null;
  }
  
  // 更新状态
  isGenerating.value = false;
  isCompleted.value = false;
  
  // 通知父组件
  emits('update:modelValue', false);
  emits('close');
};

// 抽屉完全打开后回调
const handleAfterEnter = () => {
  isTransitioning.value = false;
  // 确保DOM完全渲染后开始生成
  // if (props.content && !isGenerating.value && !isCompleted.value) {
  //   startTyping();
  // }
};

// 抽屉完全关闭后回调
const handleAfterLeave = () => {
  isTransitioning.value = false;
  // 重置内容
  displayedContent.value = '';
};

// 监听props.modelValue变化
watch(() => props.modelValue, (newVal) => {
  // 防止递归更新
  if (newVal !== isOpen.value) {
    isOpen.value = newVal;
    
    if (newVal) {
      // 抽屉打开时
      isTransitioning.value = true;
      // 重置状态
      isGenerating.value = false;
      isCompleted.value = false;
    } else {
      if (currentEventSource.value) {
        currentEventSource.value.close();
        currentEventSource.value = null;
      }
    }
  }
});

// 监听内容变化
watch(() => props.content, (newVal) => {
  // 只有当抽屉打开且内容变化时重新开始
  if (isOpen.value && !isTransitioning.value && newVal) {
    displayedContent.value=newVal;
    nextTick(() => {
        const contentEl = contentRef.value;
        if (contentEl) {
          contentEl.scrollTop = contentEl.scrollHeight;
        }
      });
  }
});

// 组件卸载时清理
onBeforeUnmount(() => {
  if (typingTimer.value) {
    clearInterval(typingTimer.value);
    typingTimer.value = null;
  }
  
  if (currentEventSource.value) {
    currentEventSource.value.close();
    currentEventSource.value = null;
  }
});
</script>




<style scoped lang="scss">
.ai-drawer-panel {
  --text-primary: #409eff;
  --text-secondary: #606266;
  --primary-color: #409eff;
  --border-color: #e5e6eb;
  --bg-color: #272822;
  
  // 使用嵌套结构提高权重
  &:deep(.el-drawer) {
    // 修复抽屉头部样式
    .el-drawer__header {
      padding: 16px 20px;
      border-bottom: 1px solid var(--border-color);
      margin-bottom: 0 !important; // 使用!important确保覆盖默认样式
      
      .el-drawer__title {
        font-size: 16px;
        color: var(--text-primary);
        font-weight: 500;
      }
    }
  }
}

.status-bar {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid var(--border-color);
  background-color: #f7f8fa;
  
  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--text-secondary);
    margin-right: 8px;
    transition: background-color 0.3s;
  }
  
  .status-dot.generating {
    background-color: var(--primary-color);
    animation: pulse 1.5s infinite;
  }
  
  .status-text {
    font-size: 13px;
    color: var(--text-secondary);
  }
}

.content-container {
  padding: 0;
  background-color: var(--bg-color);
  // 添加高度计算，确保内容区域不会溢出
  height: calc(100% - 56px); // 减去头部高度
}

.response-text {
  // 使用相对高度而非绝对高度
  height: calc(100vh - 140px);
  max-height: calc(100vh - 140px);
  overflow-y: auto;
  line-height: 1.8;
  font-size: 14px; // 增加字体大小提高可读性
  color: #999;
  // padding: 20px; // 添加内边距
  
  .content-paragraph {
    margin: 18px 0;
    &:first-child {
      margin-top: 0;
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  br {
    content: "";
    display: block;
    margin: 4px 0;
  }
  
  .empty-placeholder {
    color: var(--text-secondary);
    text-align: center;
    padding: 80px 0;
    font-size: 14px;
  }
}

// 动画效果
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

// 滚动条美化 - 增加悬停效果
.response-text::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.response-text::-webkit-scrollbar-thumb {
  background-color: rgba(144, 147, 153, 0.3);
  border-radius: 4px;
  transition: background-color 0.3s;
  
  &:hover {
    background-color: rgba(144, 147, 153, 0.5);
  }
}

.response-text::-webkit-scrollbar-track {
  background-color: transparent;
}

// 加载动画位置调整 - 增加居中效果
:deep(.el-loading-spinner) {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  
  .circular {
    width: 42px;
    height: 42px;
  }
}

// 添加响应式设计
@media (max-width: 768px) {
  .ai-drawer-panel {
    // 移动端优化
    &:deep(.el-drawer) {
      width: 100% !important;
      
      .el-drawer__header {
        padding: 12px 16px;
      }
    }
    
    .status-bar {
      padding: 10px 16px;
    }
    
    .response-text {
      font-size: 13px;
      height: calc(100vh - 120px);
      max-height: calc(100vh - 120px);
      padding: 16px;
    }
  }
}

</style>