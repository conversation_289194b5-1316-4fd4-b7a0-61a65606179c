import request from '@/utils/request'

// from web-ui/src/api/scenario/scenegroup.js
// 教师端-根据课程代码查询课程和对应的群组列表
export function getGroupsByCourseCode(courseCode) {
  return request({
    url: '/teacher/chat/group/course/' + courseCode,
    method: 'get'
  })
}

// 获取群组聊天历史消息
export function getGroupChatHistory(groupId, pageNum = 1, pageSize = 50) {
  return request({
    url: '/student/chat/messages/' + groupId,
    method: 'get',
    params: {
      pageNum: pageNum,
      pageSize: pageSize
    }
  })
}

// from web-ui/src/api/teacher/monitor.js
/**
 * 验证课程码是否有效
 * @param {string} courseCode - 课程码
 * @returns {Promise} API响应结果
 */
export function validateCourseCode(courseCode) {
  return request({
    url: `/teacher/monitor/validate-course-code/${courseCode}`,
    method: 'GET'
  })
}

/**
 * 获取课程信息
 * @param {string} courseCode - 课程码
 * @returns {Promise} API响应结果
 */
export function getCourseInfo(courseCode) {
  return request({
    url: `/teacher/monitor/course/${courseCode}`,
    method: 'GET'
  })
}

/**
 * 获取简化的课程信息
 * @param {string} courseCode - 课程码
 * @returns {Promise} API响应结果
 */
export function getCourseSimpleInfo(courseCode) {
  return request({
    url: `/teacher/monitor/course/${courseCode}/simple`,
    method: 'GET'
  })
}

/**
 * 更新课程状态
 * @param {string} courseCode - 课程码
 * @param {string} courseState - 课程状态（0-未开始，1-进行中，2-已结束）
 * @returns {Promise} API响应结果
 */
export function updateCourseState(courseCode, courseState) {
  return request({
    url: `/teacher/monitor/course/${courseCode}/state`,
    method: 'PUT',
    params: {
      courseState: courseState
    }
  })
}

/**
 * 结束课堂
 * @param {string} courseCode - 课程码
 * @returns {Promise} API响应结果
 */
export function endClass(courseCode) {
  return request({
    url: `/teacher/monitor/course/${courseCode}/end`,
    method: 'PUT'
  })
}

// from web-ui/src/api/teacher/stage.js
// 根据课程代码查询课程阶段列表
export function getCourseStageListByCourseCode(courseCode) {
  return request({
    url: '/teacher/course/stage/list/course/' + courseCode,
    method: 'get'
  })
}

// 更新课程阶段状态
export function updateCourseStageStatus(courseStageId, isCompleted) {
  return request({
    url: `/teacher/course/stage/status/${courseStageId}`,
    method: 'put',
    params: {
      isCompleted: isCompleted
    }
  })
}

// from web-ui/src/api/teacher/survey.js
// 根据课程代码查询问卷列表
export function getSurveyListByCourseCode(courseCode) {
  return request({
    url: `/teacher/survey/list/course/${courseCode}`,
    method: 'get'
  })
}

// 根据问卷ID查询问卷统计结果
export function getSurveyStatistics(paperId) {
  return request({
    url: `/teacher/survey/statistics/${paperId}`,
    method: 'get'
  })
} 